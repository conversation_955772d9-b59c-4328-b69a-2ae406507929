'use client';

import React, { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

const personalInfoSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters.'),
  middleName: z.string().optional(),
  lastName: z.string().min(2, 'Last name must be at least 2 characters.'),
  mothersName: z.string().optional(),
  email: z.string().email('Please enter a valid email address.').optional().or(z.literal('')),
  contact: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.'),
  contact2: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.')
    .optional()
    .or(z.literal('')),
  gender: z.string().optional(),
  birthday: z.date({ required_error: 'Please select your date of birth' }),
  school: z.string().min(2, 'School name must be at least 2 characters.'),
  address: z.string().min(5, 'Address must be at least 5 characters.'),
  age: z.string().optional(),
  aadhaarNumber: z
    .string()
    .min(12, 'Aadhaar number must be 12 digits.')
    .max(12, 'Aadhaar number must be 12 digits.')
    .regex(/^\d+$/, 'Aadhaar number must contain only digits.')
    .optional()
    .or(z.literal('')),
  bloodGroup: z.string().optional(),
  birthPlace: z.string().optional(),
  motherTongue: z.string().optional(),
  religion: z.string().optional(),
  caste: z.string().optional(),
  subCaste: z.string().optional(),
});

type PersonalInfoFormValues = z.infer<typeof personalInfoSchema>;

export function PersonalInfoForm() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const { profileData, loading: profileLoading } = useSelector(
    (state: RootState) => state.studentProfile
  );

  const form = useForm<PersonalInfoFormValues>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      firstName: '',
      middleName: '',
      lastName: '',
      mothersName: '',
      email: '',
      contact: '',
      contact2: '',
      gender: '',
      birthday: undefined,
      school: '',
      address: '',
      age: '',
      aadhaarNumber: '',
      bloodGroup: '',
      birthPlace: '',
      motherTongue: '',
      religion: '',
      caste: '',
      subCaste: '',
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (!profileData) return;

    const profileObj = profileData.profile;
    const studentData = profileObj?.student || JSON.parse(localStorage.getItem('student_data') || '{}');

    const formValues = {
      firstName: studentData?.firstName || '',
      middleName: studentData?.middleName || '',
      lastName: studentData?.lastName || '',
      mothersName: studentData?.mothersName || '',
      email: studentData?.email || '',
      contact: studentData?.contact || '',
      contact2: profileObj?.contactNo2 || '',
      gender: profileObj?.gender || '',
      birthday: profileObj?.birthday ? new Date(profileObj.birthday) : undefined,
      school: profileObj?.school || '',
      address: profileObj?.address || '',
      age: profileObj?.age?.toString() || '',
      aadhaarNumber: profileObj?.aadhaarNo || '',
      bloodGroup: profileObj?.bloodGroup || '',
      birthPlace: profileObj?.birthPlace || '',
      motherTongue: profileObj?.motherTongue || '',
      religion: profileObj?.religion || '',
      caste: profileObj?.caste || '',
      subCaste: profileObj?.subCaste || '',
    };

    const currentValues = form.getValues();
    const isFormEmpty = !currentValues.firstName && !currentValues.lastName && !currentValues.contact;

    if (isFormEmpty) {
      form.reset(formValues);
    }
  }, [profileData, form]);

  const onSubmit = async (data: PersonalInfoFormValues) => {
    setIsSubmitting(true);

    try {
      const jsonData: any = {
        firstName: data.firstName,
        middleName: data.middleName,
        lastName: data.lastName,
        mothersName: data.mothersName,
        email: data.email,
        contact: data.contact,
        contact2: data.contact2,
        gender: data.gender,
        birthday: data.birthday?.toISOString() || '',
        school: data.school,
        address: data.address,
        age: data.age,
        aadhaarNumber: data.aadhaarNumber,
        bloodGroup: data.bloodGroup,
        birthPlace: data.birthPlace,
        motherTongue: data.motherTongue,
        religion: data.religion,
        caste: data.caste,
        subCaste: data.subCaste,
      };

      const result = await dispatch(updateStudentProfile(jsonData));

      if (result.meta.requestStatus === 'fulfilled') {
        toast.success('Personal information saved successfully!');
        
        // Update localStorage
        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');
        const studentData = {
          ...existingStudentData,
          firstName: data.firstName,
          middleName: data.middleName,
          lastName: data.lastName,
          mothersName: data.mothersName,
          email: data.email || existingStudentData.email,
          contact: data.contact,
        };
        localStorage.setItem('student_data', JSON.stringify(studentData));
        
        await dispatch(fetchStudentProfile());
        router.push('/student/profile/educational-info');
      } else {
        toast.error('Failed to save personal information');
      }
    } catch {
      toast.error('Failed to save personal information');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (profileLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <svg
          className="animate-spin h-10 w-10 text-black mb-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        <p className="text-gray-600">Loading profile information...</p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name *</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter First Name" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="middleName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Middle Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter Middle Name" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name *</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter Last Name" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="mothersName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mother's Name</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter Mother's Name" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Enter Email" type="email" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="contact"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Number *</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="8520369851"
                    type="tel"
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '');
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="contact2"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Number 2</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Enter Alternate Number"
                    type="tel"
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '');
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isSubmitting ? 'Saving...' : 'Save & Continue'}
        </Button>
      </form>
    </Form>
  );
}
