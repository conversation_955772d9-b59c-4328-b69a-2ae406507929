'use client';

import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

interface PersonalInfoFormProps {
  form: UseFormReturn<any>;
}

export function PersonalInfoForm({ form }: PersonalInfoFormProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">First Name *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                  placeholder="Enter First Name"
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="middleName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">Middle Name</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                  placeholder="Enter Middle Name"
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">Last Name *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                  placeholder="Enter Last Name"
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="mothersName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">Mother's Name</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                  placeholder="Enter Mother's Name"
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
      </div>
      
      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-black font-medium">Email</FormLabel>
            <FormControl>
              <Input
                {...field}
                className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                placeholder="Enter Email"
                type="email"
              />
            </FormControl>
            <FormMessage className="text-red-500" />
          </FormItem>
        )}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          control={form.control}
          name="contact"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">Contact Number *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                  placeholder="8520369851"
                  type="tel"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  onKeyDown={(e) => {
                    const specialKeys = [
                      'Backspace',
                      'Tab',
                      'Enter',
                      'Escape',
                      'Delete',
                      'ArrowLeft',
                      'ArrowRight',
                      'Home',
                      'End',
                    ];
                    if (specialKeys.includes(e.key)) {
                      return;
                    }
                    if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
                      return;
                    }
                    if (!/^\d$/.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '');
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contact2"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">Contact Number 2</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                  placeholder="Enter Alternate Number"
                  type="tel"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  onKeyDown={(e) => {
                    const specialKeys = [
                      'Backspace',
                      'Tab',
                      'Enter',
                      'Escape',
                      'Delete',
                      'ArrowLeft',
                      'ArrowRight',
                      'Home',
                      'End',
                    ];
                    if (specialKeys.includes(e.key)) {
                      return;
                    }
                    if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
                      return;
                    }
                    if (!/^\d$/.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '');
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <FormField
          control={form.control}
          name="gender"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">Gender</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value || undefined}
              >
                <FormControl>
                  <SelectTrigger className="bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent className="bg-white w-[var(--radix-select-trigger-width)]">
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="age"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-black font-medium">Age</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                  placeholder="Enter Age"
                  type="number"
                  min="1"
                  max="100"
                />
              </FormControl>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="birthday"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel className="text-black font-medium">Date of Birth</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (
                        format(field.value, 'PPP')
                      ) : (
                        <span>Select your birthday</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-white border border-gray-300 shadow-lg" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                    month={field.value || new Date()}
                    className="rounded-md border-0"
                  />
                </PopoverContent>
              </Popover>
              <FormDescription className="text-xs text-gray-500">
                Your date of birth will be verified with your documents
              </FormDescription>
              <FormMessage className="text-red-500" />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
