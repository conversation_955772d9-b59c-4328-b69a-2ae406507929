<div id="student-details" class="content" role="tabpanel" aria-labelledby="student-details-trigger">
    <form id="student-details-forms" method="POST" enctype="multipart/form-data">
        @csrf
        <input type="hidden" name="student_id" />
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="firstName" class="form-label">First Name *</label>
                    <input type="text" name="firstName" id="firstName" class="form-control firstcap"
                        placeholder="Enter First Name">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="middleName" class="form-label">Middle Name *</label>
                    <input type="text" name="middleName" id="middleName" class="form-control firstcap"
                        placeholder="Enter Middle Name">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="lastName" class="form-label">Last Name *</label>
                    <input type="text" name="lastName" id="lastName" class="form-control firstcap"
                        placeholder="Enter Last Name">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="mothersName" class="form-label">Mother's Name *</label>
                    <input type="text" name="mothersName" id="mothersName" class="form-control firstcap"
                        placeholder="Enter Mother's Name">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" name="email" id="email" class="form-control" placeholder="Enter Email">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="contact" class="form-label">Contact Number *</label>
                    <input type="text" name="contact" id="contact" class="form-control"
                        placeholder="Enter Contact Number" readonly>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="contactNo2" class="form-label">Contact Number 2</label>
                    <input type="text" name="contactNo2" id="contactNo2" class="form-control"
                        placeholder="Enter Alternate Number">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="medium" class="form-label">Medium *</label>
                    <select name="medium" id="medium" class="form-control select2">
                        <option value="">Select</option>
                        @foreach (['gujarati', 'english'] as $bg)
                        <option value="{{ $bg }}">{{ $bg }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="classroom_name" class="form-label">Standard *</label>
                    <select name="classroom_name" id="classroom_name" class="form-control select2">
                        <option value="">Select</option>
                    </select>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="gender" class="form-label">Gender *</label>
                    <select name="gender" id="gender" class="form-control select2">
                        <option value="">Select</option>
                        @foreach (['Male', 'Female', 'Other'] as $bg)
                        <option value="{{ $bg }}">{{ $bg }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="birthday" class="form-label">Date of Birth</label>
                    <input type="text" name="birthday" id="birthday" class="form-control datepicker">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="school" class="form-label">School Name *</label>
                    <input type="text" name="school" id="school" class="form-control"
                        placeholder="Enter School">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="address" class="form-label">Address</label>
                    <input type="text" name="address" id="address" class="form-control"
                        placeholder="Enter Address">
                </div>
            </div>

            {{-- Age --}}
            <div class="col-md-6">
                <div class="form-group">
                    <label for="age" class="form-label">Age</label>
                    <input type="number" name="age" id="age" class="form-control"
                        placeholder="Enter Age">
                </div>
            </div>

            {{-- Aadhaar Number --}}
            <div class="col-md-6">
                <div class="form-group">
                    <label for="aadhaarNo" class="form-label">Aadhaar Number</label>
                    <input type="text" name="aadhaarNo" id="aadhaarNo" class="form-control"
                        placeholder="Enter Aadhaar No">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="bloodGroup" class="form-label">Blood Group</label>
                    <select name="bloodGroup" id="bloodGroup" class="form-control select2">
                        <option value="">Select</option>
                        @foreach (['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'] as $bg)
                        <option value="{{ $bg }}">{{ $bg }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="birthPlace" class="form-label">Birth Place</label>
                    <input type="text" name="birthPlace" id="birthPlace" class="form-control firstcap"
                        placeholder="Enter Birth Place">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="motherTongue" class="form-label">Mother Tongue</label>
                    <input type="text" name="motherTongue" id="motherTongue" class="form-control firstcap"
                        placeholder="Enter Mother Tongue">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="religion" class="form-label">Religion</label>
                    <input type="text" name="religion" id="religion" class="form-control firstcap"
                        placeholder="Enter Religion">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="caste" class="form-label">Caste</label>
                    <input type="text" name="caste" id="caste" class="form-control firstcap"
                        placeholder="Enter Caste">
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="subCaste" class="form-label">Sub Caste</label>
                    <input type="text" name="subCaste" id="subCaste" class="form-control firstcap"
                        placeholder="Enter Sub Caste">
                </div>
            </div>
            <div class="form-group ast-required col-sm-12">
                <label>Student Image (Only jpg,jpeg,png Allowed)</label>
                <div class="imageWrapper ">
                    <img class="image-product-logo" style="height:200px;width:200px"
                        src="http://dummyimage.com/400x200/f5f5f5/000000&text=Student+Photo">
                    <button class="file-upload">
                        <input type="file" name="photo" class="file-input form-control" accept=".jpg,.jpeg,.png,image/jpeg,image/jpg,image/png">Choose Photo
                    </button>
                </div>
            </div>
        </div>

        <p><b>Academic Enrollment Details</b></p>

        <input type="hidden" name="year" value="{{ getActiveYearId() }}" />

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Department *</label>
                    <select id="department" class="form-control select2" name="department">
                        <option value="">Select department</option>
                        @foreach ($department as $value)
                        <option value="{{ $value->id }}">{{ $value->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Classroom *</label>
                    <select id="classroom" class="form-control select2 classroom-data" name="classroom">
                        <option value="">Select Classroom</option>
                    </select>
                </div>
            </div>
        </div>
        <button class="btn btn-primary" id="submit-student-details">Enroll Student</button>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        const apiUrl ="{{ env('UEST_FRONTEND_URL', 'http://localhost:4005') }}/api/v1/admin/constants/classroom-data";

        const ajaxParams = {
            url: apiUrl,
            requestType: "GET",
            data: {},
            successCallbackFunction: function (response) {
                if (response.success && response.data) {
                    $('#classroom_name').html('<option value="">Select</option>');

                    response.data.forEach(item => {
                        const option = `<option value="${item.name}">${item.name}</option>`;
                        $('#classroom_name').append(option);
                    });
                } else {
                    toastr.error("Failed to load classroom data.");
                }
            }
        };

        commonAjax(ajaxParams);
    });

    function previewImage(input) {
        if (input.files && input.files[0]) {
            var file = input.files[0];

            var validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!validTypes.includes(file.type)) {
                toastr.error('Please select a valid image file (JPG, JPEG, PNG)');
                input.value = '';
                return;
            }

            if (file.size > 5 * 1024 * 1024) {
                toastr.error('File size should be less than 5MB');
                input.value = '';
                return;
            }

            var reader = new FileReader();
            reader.onload = function(e) {
                $('.image-product-logo').attr('src', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    }

    function submitPhotoOnly() {
        var studentId = $('[name="student_id"]').val();
        if (!studentId) {
            toastr.error('Student ID not found. Please save the form first.');
            return;
        }

        var formData = new FormData();
        var fileInput = $('input[name="photo"]')[0];

        if (fileInput.files[0]) {
            formData.append('photo', fileInput.files[0]);
            formData.append('student_id', studentId);
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

            $.ajax({
                url: "{{ route('storeStudentDetails') }}",
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    $('.page-loader').show();
                },
                success: function(result) {
                    if (result.success) {
                        toastr.success('Photo updated successfully!');
                        setTimeout(function() {
                            if (typeof loadStudentImage === 'function') {
                                loadStudentImage(studentId);
                            }
                        }, 500);
                    } else {
                        toastr.error(result.error || 'Failed to update photo');
                    }
                },
                complete: function() {
                    $('.page-loader').hide();
                },
                error: function(err) {
                    if (err.status === 422) {
                        showErrors(err.responseJSON.errors);
                    } else {
                        toastr.error('Failed to update photo. Please try again.');
                    }
                }
            });
        }
    }

    $(document).ready(function() {
        $('input[name="photo"]').on('change', function() {
            previewImage(this);

            var studentId = $('[name="student_id"]').val();
            if (studentId && this.files[0]) {
                submitPhotoOnly();
            }
        });
    });
</script>
