{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div data-slot=\"form-item\" className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YAAI,aAAU;YAAY,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAa,GAAG,KAAK;;;;;;;;;;;AAGlF;IARS;MAAA;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAyD;;IAC1F,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAZS;;QACuB;;;MADvB;AAcT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAZS;;QACyD;;;MADzD;AAcT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,6LAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;MAFS;AAIT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/documents/documents-form.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { useForm } from 'react-hook-form';\nimport { z } from 'zod';\nimport { toast } from 'sonner';\nimport { useRouter } from 'next/navigation';\nimport { Camera, Upload, X } from 'lucide-react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '@/store';\nimport { updateStudentProfile } from '@/store/thunks/studentProfileThunks';\nimport { updateProfilePhoto } from '@/store/slices/studentProfileSlice';\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/form';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport Image from 'next/image';\n\nconst documentsFormSchema = z.object({\n  photo: z.any().optional(),\n  document: z.any().optional(),\n});\n\ntype DocumentsFormValues = z.infer<typeof documentsFormSchema>;\n\nexport function DocumentsForm() {\n  const router = useRouter();\n  const dispatch = useDispatch<AppDispatch>();\n\n  const [photo, setPhoto] = useState<string | null>(null);\n  const [isCameraOpen, setIsCameraOpen] = useState(false);\n  const [cameraError, setCameraError] = useState<string | null>(null);\n  const [uploadedDocument, setUploadedDocument] = useState<\n    File | { name: string; size: number; url: string; type: string } | null\n  >(null);\n  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);\n\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\n  const profile = profileData?.profile || null;\n\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  const form = useForm<DocumentsFormValues>({\n    resolver: zodResolver(documentsFormSchema),\n    defaultValues: {\n      photo: undefined,\n      document: undefined,\n    },\n    mode: 'onSubmit',\n  });\n\n  React.useEffect(() => {\n    if (profileData) {\n      const profileObj = profileData.profile;\n\n      if (profileObj?.photo && !photo) {\n        setPhoto(profileObj.photo);\n        form.setValue('photo', profileObj.photo);\n      }\n\n      if (profileObj?.documentUrl && !uploadedDocument && !isDocumentRemoved) {\n        const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';\n        const documentUrl = profileObj.documentUrl.startsWith('http')\n          ? profileObj.documentUrl\n          : `${baseUrl}${profileObj.documentUrl}`;\n\n        const documentObj = {\n          name: documentUrl.split('/').pop() || 'Uploaded Document',\n          size: 0,\n          url: documentUrl,\n          type: 'application/octet-stream',\n        };\n\n        setUploadedDocument(documentObj);\n        form.setValue('document', documentObj);\n      }\n    }\n  }, [profileData, form, photo, uploadedDocument, isDocumentRemoved]);\n\n  const openCamera = async () => {\n    setCameraError(null);\n\n    try {\n      if (!navigator.mediaDevices?.getUserMedia) {\n        throw new Error('Camera not supported on this device');\n      }\n\n      setIsCameraOpen(true);\n\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { facingMode: 'user' },\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        videoRef.current.onloadedmetadata = () => {\n          videoRef.current?.play().catch(() => toast.error('Error starting camera preview'));\n        };\n      }\n    } catch (error: any) {\n      setIsCameraOpen(false);\n      const message = error.name === 'NotAllowedError'\n        ? 'Please allow camera access in your browser settings.'\n        : 'Could not access camera. Please check your camera settings.';\n      setCameraError(message);\n      toast.error(message);\n    }\n  };\n\n  const compressImage = (canvas: HTMLCanvasElement, maxWidth: number = 800, quality: number = 0.6): string => {\n    const context = canvas.getContext('2d');\n    if (!context) return '';\n\n    const originalWidth = canvas.width;\n    const originalHeight = canvas.height;\n\n    let newWidth = originalWidth;\n    let newHeight = originalHeight;\n\n    if (originalWidth > maxWidth) {\n      newWidth = maxWidth;\n      newHeight = (originalHeight * maxWidth) / originalWidth;\n    }\n\n    const compressedCanvas = document.createElement('canvas');\n    compressedCanvas.width = newWidth;\n    compressedCanvas.height = newHeight;\n\n    const compressedContext = compressedCanvas.getContext('2d');\n    if (!compressedContext) return '';\n\n    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);\n\n    return compressedCanvas.toDataURL('image/jpeg', quality);\n  };\n\n  const capturePhoto = () => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext('2d');\n\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    context?.clearRect(0, 0, canvas.width, canvas.height);\n    context?.save();\n    context?.scale(-1, 1);\n    context?.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);\n    context?.restore();\n\n    const compressedPhotoDataUrl = compressImage(canvas, 800, 0.6);\n    const base64Data = compressedPhotoDataUrl.split(',')[1];\n    const sizeInKB = (base64Data.length * 3) / 4 / 1024;\n\n    if (sizeInKB > 5120) {\n      toast.error('Photo size exceeds 5MB limit. Please try again.');\n      return;\n    }\n\n    setPhoto(compressedPhotoDataUrl);\n    form.setValue('photo', compressedPhotoDataUrl);\n    dispatch(updateProfilePhoto(compressedPhotoDataUrl));\n\n    closeCamera();\n  };\n\n  const closeCamera = () => {\n    if (videoRef.current?.srcObject) {\n      const stream = videoRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      videoRef.current.srcObject = null;\n    }\n    setIsCameraOpen(false);\n    setCameraError(null);\n  };\n\n  const handleDocumentUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('File size must be less than 5MB');\n      return;\n    }\n\n    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];\n    if (!allowedTypes.includes(file.type)) {\n      toast.error('Only JPEG, PNG, and PDF files are allowed');\n      return;\n    }\n\n    setUploadedDocument(file);\n    setIsDocumentRemoved(false);\n    form.setValue('document', file);\n  };\n\n  const removeDocument = () => {\n    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {\n      URL.revokeObjectURL(uploadedDocument.url);\n    }\n\n    setUploadedDocument(null);\n    setIsDocumentRemoved(true);\n    const fileInput = document.getElementById('document') as HTMLInputElement;\n    if (fileInput) fileInput.value = '';\n    form.setValue('document', null);\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes < 1024) return bytes + ' bytes';\n    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';\n    else return (bytes / 1048576).toFixed(1) + ' MB';\n  };\n\n  const onSubmit = async (data: DocumentsFormValues) => {\n    try {\n      const currentPhoto = photo || profileData?.profile?.photo;\n      if (!currentPhoto) {\n        toast.error('Please capture a photo for your profile');\n        return;\n      }\n\n      if (!uploadedDocument || isDocumentRemoved) {\n        toast.error('Identity document is required. Please upload a document.');\n        return;\n      }\n\n      const jsonData: any = {};\n\n      if (photo?.startsWith('data:')) {\n        const base64Data = photo.split(',')[1];\n        const sizeInKB = (base64Data.length * 3) / 4 / 1024;\n\n        if (sizeInKB > 5120) {\n          toast.error('Photo size exceeds 5MB limit.');\n          return;\n        }\n\n        jsonData.photo = base64Data;\n        jsonData.photoMimeType = 'image/jpeg';\n      }\n\n      if (uploadedDocument instanceof File || (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:'))) {\n        const documentFile = uploadedDocument instanceof File\n          ? uploadedDocument\n          : await fetch(uploadedDocument.url)\n              .then(res => res.blob())\n              .then(blob => new File([blob], uploadedDocument.name, { type: uploadedDocument.type }));\n\n        const documentBase64 = await new Promise<string>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = () => resolve((reader.result as string).split(',')[1]);\n          reader.onerror = reject;\n          reader.readAsDataURL(documentFile);\n        });\n\n        const docSizeKB = (documentBase64.length * 3) / 4 / 1024;\n        if (docSizeKB > 5120) {\n          toast.error('Document size exceeds 5MB limit.');\n          return;\n        }\n\n        jsonData.document = documentBase64;\n        jsonData.documentMimeType = documentFile.type;\n        jsonData.documentName = documentFile.name;\n      }\n\n      if (isDocumentRemoved && profileData?.profile?.documentUrl) {\n        jsonData.removeDocument = true;\n      }\n\n      const result = await dispatch(updateStudentProfile(jsonData));\n\n      if (result.meta.requestStatus === 'fulfilled') {\n        toast.success('Profile completed successfully!');\n\n        // Navigate based on query parameters\n        const searchParams = new URLSearchParams(window.location.search);\n        const fromQuiz = searchParams.get('quiz') === 'true';\n        const examId = searchParams.get('examId');\n\n        if (fromQuiz) {\n          if (examId) {\n            router.push(`/uwhiz-exam/${examId}`);\n          } else {\n            router.push('/mock-test');\n          }\n        } else {\n          router.push('/');\n        }\n      } else {\n        toast.error('Failed to upload documents');\n      }\n    } catch (error) {\n      toast.error('Failed to upload documents');\n    }\n  };\n\n  return (\n    <Form {...form}>\n      <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n        {/* Photo Section */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Profile Photo</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {photo ? (\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"relative\">\n                  <Image\n                    src={photo}\n                    alt=\"Profile\"\n                    width={150}\n                    height={150}\n                    className=\"rounded-full object-cover border-4 border-gray-200\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"destructive\"\n                    size=\"sm\"\n                    className=\"absolute -top-2 -right-2 rounded-full w-8 h-8 p-0\"\n                    onClick={() => {\n                      setPhoto(null);\n                      form.setValue('photo', null);\n                    }}\n                  >\n                    <X className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n                <Button type=\"button\" variant=\"outline\" onClick={openCamera}>\n                  <Camera className=\"w-4 h-4 mr-2\" />\n                  Retake Photo\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"w-32 h-32 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center\">\n                  <Camera className=\"w-8 h-8 text-gray-400\" />\n                </div>\n                <Button type=\"button\" onClick={openCamera}>\n                  <Camera className=\"w-4 h-4 mr-2\" />\n                  Take Photo\n                </Button>\n              </div>\n            )}\n\n            {isCameraOpen && (\n              <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n                <div className=\"bg-white p-6 rounded-lg max-w-md w-full mx-4\">\n                  <div className=\"flex justify-between items-center mb-4\">\n                    <h3 className=\"text-lg font-semibold\">Take Photo</h3>\n                    <Button type=\"button\" variant=\"ghost\" size=\"sm\" onClick={closeCamera}>\n                      <X className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                  <div className=\"space-y-4\">\n                    <video\n                      ref={videoRef}\n                      className=\"w-full rounded-lg\"\n                      autoPlay\n                      playsInline\n                      muted\n                      style={{ transform: 'scaleX(-1)' }}\n                    />\n                    <div className=\"flex justify-center space-x-4\">\n                      <Button type=\"button\" onClick={capturePhoto}>\n                        Capture\n                      </Button>\n                      <Button type=\"button\" variant=\"outline\" onClick={closeCamera}>\n                        Cancel\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <canvas ref={canvasRef} style={{ display: 'none' }} />\n          </CardContent>\n        </Card>\n\n        {/* Document Section */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Identity Document</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {uploadedDocument ? (\n              <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <Upload className=\"w-5 h-5 text-blue-600\" />\n                  </div>\n                  <div>\n                    <p className=\"font-medium\">{uploadedDocument.name}</p>\n                    <p className=\"text-sm text-gray-500\">\n                      {uploadedDocument.size ? formatFileSize(uploadedDocument.size) : 'Unknown size'}\n                    </p>\n                  </div>\n                </div>\n                <Button\n                  type=\"button\"\n                  variant=\"destructive\"\n                  size=\"sm\"\n                  onClick={removeDocument}\n                >\n                  <X className=\"w-4 h-4\" />\n                </Button>\n              </div>\n            ) : (\n              <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                <Upload className=\"w-8 h-8 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-sm text-gray-600 mb-4\">\n                  Upload your identity document (Aadhaar, Passport, etc.)\n                </p>\n                <input\n                  id=\"document\"\n                  type=\"file\"\n                  accept=\".jpg,.jpeg,.png,.pdf\"\n                  onChange={handleDocumentUpload}\n                  className=\"hidden\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => document.getElementById('document')?.click()}\n                >\n                  Choose File\n                </Button>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        <Button type=\"submit\">Complete Profile</Button>\n      </form>\n    </Form>\n  );\n}\n"], "names": [], "mappings": ";;;AAqEwB;;AAnExB;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAQA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;;;AAyBA,MAAM,sBAAsB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,uIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;IACvB,UAAU,uIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;AAC5B;AAIO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAErD;IACF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;qCAAE,CAAC,QAAqB,MAAM,cAAc;;IAC9E,MAAM,UAAU,aAAa,WAAW;IAExC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAuB;QACxC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;QACZ;QACA,MAAM;IACR;IAEA,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,aAAa;gBACf,MAAM,aAAa,YAAY,OAAO;gBAEtC,IAAI,YAAY,SAAS,CAAC,OAAO;oBAC/B,SAAS,WAAW,KAAK;oBACzB,KAAK,QAAQ,CAAC,SAAS,WAAW,KAAK;gBACzC;gBAEA,IAAI,YAAY,eAAe,CAAC,oBAAoB,CAAC,mBAAmB;oBACtE,MAAM,UAAU,8DAAwC;oBACxD,MAAM,cAAc,WAAW,WAAW,CAAC,UAAU,CAAC,UAClD,WAAW,WAAW,GACtB,GAAG,UAAU,WAAW,WAAW,EAAE;oBAEzC,MAAM,cAAc;wBAClB,MAAM,YAAY,KAAK,CAAC,KAAK,GAAG,MAAM;wBACtC,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAEA,oBAAoB;oBACpB,KAAK,QAAQ,CAAC,YAAY;gBAC5B;YACF;QACF;kCAAG;QAAC;QAAa;QAAM;QAAO;QAAkB;KAAkB;IAElE,MAAM,aAAa;QACjB,eAAe;QAEf,IAAI;YACF,IAAI,CAAC,UAAU,YAAY,EAAE,cAAc;gBACzC,MAAM,IAAI,MAAM;YAClB;YAEA,gBAAgB;YAEhB,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBAAE,YAAY;gBAAO;YAC9B;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,SAAS,OAAO,CAAC,gBAAgB,GAAG;oBAClC,SAAS,OAAO,EAAE,OAAO,MAAM,IAAM,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACnD;YACF;QACF,EAAE,OAAO,OAAY;YACnB,gBAAgB;YAChB,MAAM,UAAU,MAAM,IAAI,KAAK,oBAC3B,yDACA;YACJ,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC,QAA2B,WAAmB,GAAG,EAAE,UAAkB,GAAG;QAC7F,MAAM,UAAU,OAAO,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,gBAAgB,OAAO,KAAK;QAClC,MAAM,iBAAiB,OAAO,MAAM;QAEpC,IAAI,WAAW;QACf,IAAI,YAAY;QAEhB,IAAI,gBAAgB,UAAU;YAC5B,WAAW;YACX,YAAY,AAAC,iBAAiB,WAAY;QAC5C;QAEA,MAAM,mBAAmB,SAAS,aAAa,CAAC;QAChD,iBAAiB,KAAK,GAAG;QACzB,iBAAiB,MAAM,GAAG;QAE1B,MAAM,oBAAoB,iBAAiB,UAAU,CAAC;QACtD,IAAI,CAAC,mBAAmB,OAAO;QAE/B,kBAAkB,SAAS,CAAC,QAAQ,GAAG,GAAG,UAAU;QAEpD,OAAO,iBAAiB,SAAS,CAAC,cAAc;IAClD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;QAE7C,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,UAAU,OAAO,UAAU,CAAC;QAElC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,SAAS,UAAU,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACpD,SAAS;QACT,SAAS,MAAM,CAAC,GAAG;QACnB,SAAS,UAAU,OAAO,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACvE,SAAS;QAET,MAAM,yBAAyB,cAAc,QAAQ,KAAK;QAC1D,MAAM,aAAa,uBAAuB,KAAK,CAAC,IAAI,CAAC,EAAE;QACvD,MAAM,WAAW,AAAC,WAAW,MAAM,GAAG,IAAK,IAAI;QAE/C,IAAI,WAAW,MAAM;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,SAAS;QACT,KAAK,QAAQ,CAAC,SAAS;QACvB,SAAS,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE;QAE5B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,SAAS,OAAO,EAAE,WAAW;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;QACA,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAkB;QAChF,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,oBAAoB;QACpB,qBAAqB;QACrB,KAAK,QAAQ,CAAC,YAAY;IAC5B;IAEA,MAAM,iBAAiB;QACrB,IAAI,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAU;YAC7F,IAAI,eAAe,CAAC,iBAAiB,GAAG;QAC1C;QAEA,oBAAoB;QACpB,qBAAqB;QACrB,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,WAAW,UAAU,KAAK,GAAG;QACjC,KAAK,QAAQ,CAAC,YAAY;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,MAAM,OAAO,QAAQ;aAC5B,IAAI,QAAQ,SAAS,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK;aACxD,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;IAC7C;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,eAAe,SAAS,aAAa,SAAS;YACpD,IAAI,CAAC,cAAc;gBACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI,CAAC,oBAAoB,mBAAmB;gBAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,WAAgB,CAAC;YAEvB,IAAI,OAAO,WAAW,UAAU;gBAC9B,MAAM,aAAa,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtC,MAAM,WAAW,AAAC,WAAW,MAAM,GAAG,IAAK,IAAI;gBAE/C,IAAI,WAAW,MAAM;oBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBAEA,SAAS,KAAK,GAAG;gBACjB,SAAS,aAAa,GAAG;YAC3B;YAEA,IAAI,4BAA4B,QAAS,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAW;gBACnI,MAAM,eAAe,4BAA4B,OAC7C,mBACA,MAAM,MAAM,iBAAiB,GAAG,EAC7B,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA,OAAQ,IAAI,KAAK;wBAAC;qBAAK,EAAE,iBAAiB,IAAI,EAAE;wBAAE,MAAM,iBAAiB,IAAI;oBAAC;gBAE1F,MAAM,iBAAiB,MAAM,IAAI,QAAgB,CAAC,SAAS;oBACzD,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,AAAC,OAAO,MAAM,CAAY,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrE,OAAO,OAAO,GAAG;oBACjB,OAAO,aAAa,CAAC;gBACvB;gBAEA,MAAM,YAAY,AAAC,eAAe,MAAM,GAAG,IAAK,IAAI;gBACpD,IAAI,YAAY,MAAM;oBACpB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBAEA,SAAS,QAAQ,GAAG;gBACpB,SAAS,gBAAgB,GAAG,aAAa,IAAI;gBAC7C,SAAS,YAAY,GAAG,aAAa,IAAI;YAC3C;YAEA,IAAI,qBAAqB,aAAa,SAAS,aAAa;gBAC1D,SAAS,cAAc,GAAG;YAC5B;YAEA,MAAM,SAAS,MAAM,SAAS,CAAA,GAAA,iJAAA,CAAA,uBAAoB,AAAD,EAAE;YAEnD,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;gBAC7C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,qCAAqC;gBACrC,MAAM,eAAe,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;gBAC/D,MAAM,WAAW,aAAa,GAAG,CAAC,YAAY;gBAC9C,MAAM,SAAS,aAAa,GAAG,CAAC;gBAEhC,IAAI,UAAU;oBACZ,IAAI,QAAQ;wBACV,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ;oBACrC,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YAAK,UAAU,KAAK,YAAY,CAAC;YAAW,WAAU;;8BAErD,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB,sBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,SAAS;wDACT,KAAK,QAAQ,CAAC,SAAS;oDACzB;8DAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGjB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAQ;4CAAU,SAAS;;8DAC/C,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;yDAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAS;;8DAC7B,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAMxC,8BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,SAAQ;wDAAQ,MAAK;wDAAK,SAAS;kEACvD,cAAA,6LAAC,+LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,KAAK;wDACL,WAAU;wDACV,QAAQ;wDACR,WAAW;wDACX,KAAK;wDACL,OAAO;4DAAE,WAAW;wDAAa;;;;;;kEAEnC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAS,SAAS;0EAAc;;;;;;0EAG7C,6LAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAS,SAAQ;gEAAU,SAAS;0EAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASxE,6LAAC;oCAAO,KAAK;oCAAW,OAAO;wCAAE,SAAS;oCAAO;;;;;;;;;;;;;;;;;;8BAKrD,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,iCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAe,iBAAiB,IAAI;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEACV,iBAAiB,IAAI,GAAG,eAAe,iBAAiB,IAAI,IAAI;;;;;;;;;;;;;;;;;;kDAIvE,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;kDAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;qDAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,QAAO;wCACP,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,SAAS,cAAc,CAAC,aAAa;kDACrD;;;;;;;;;;;;;;;;;;;;;;;8BAQT,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAK;8BAAS;;;;;;;;;;;;;;;;;AAI9B;GAjagB;;QACC,qIAAA,CAAA,YAAS;QACP,4JAAA,CAAA,cAAW;QAUJ,4JAAA,CAAA,cAAW;QAMtB,iKAAA,CAAA,UAAO;;;KAlBN", "debugId": null}}]}