module.exports = {

"[project]/.next-internal/server/app/student/profile/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/not-found.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/student/profile/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
const profileFormSchema = z.object({
    firstName: z.string().min(2, 'First name must be at least 2 characters.'),
    middleName: z.string().optional(),
    lastName: z.string().min(2, 'Last name must be at least 2 characters.'),
    mothersName: z.string().optional(),
    email: z.string().email('Please enter a valid email address.').optional().or(z.literal('')),
    contact: z.string().min(10, 'Contact number must be at least 10 digits.').max(15, 'Contact number must not exceed 15 digits.').regex(/^\d+$/, 'Contact number must contain only digits.'),
    contact2: z.string().min(10, 'Contact number must be at least 10 digits.').max(15, 'Contact number must not exceed 15 digits.').regex(/^\d+$/, 'Contact number must contain only digits.').optional().or(z.literal('')),
    medium: z.string().min(1, 'Medium of instruction is required'),
    classroom: z.string().min(1, 'Standard is required'),
    gender: z.string().optional(),
    birthday: z.date({
        required_error: 'Please select your date of birth'
    }),
    school: z.string().min(2, 'School name must be at least 2 characters.'),
    address: z.string().min(5, 'Address must be at least 5 characters.'),
    age: z.string().optional(),
    aadhaarNumber: z.string().min(12, 'Aadhaar number must be 12 digits.').max(12, 'Aadhaar number must be 12 digits.').regex(/^\d+$/, 'Aadhaar number must contain only digits.').optional().or(z.literal('')),
    bloodGroup: z.string().optional(),
    birthPlace: z.string().optional(),
    motherTongue: z.string().optional(),
    religion: z.string().optional(),
    caste: z.string().optional(),
    subCaste: z.string().optional(),
    photo: z.any().optional(),
    document: z.any().optional()
});
const StudentProfileContent = ()=>{
    const router = useRouter();
    const dispatch = useDispatch();
    const searchParams = useSearchParams();
    const fromQuiz = searchParams.get('quiz') === 'true';
    const examId = searchParams.get('examId');
    const [currentStep, setCurrentStep] = useState(1);
    const [photo, setPhoto] = useState(null);
    const [isCameraOpen, setIsCameraOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [cameraError, setCameraError] = useState(null);
    const [uploadedDocument, setUploadedDocument] = useState(null);
    const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);
    const steps = [
        {
            id: 1,
            title: 'Personal Information',
            description: 'Basic personal details'
        },
        {
            id: 2,
            title: 'Educational Information',
            description: 'Academic details'
        },
        {
            id: 3,
            title: 'Documents & Photo',
            description: 'Upload required documents'
        }
    ];
    const { profileData, loading: profileLoading } = useSelector((state)=>state.studentProfile);
    const profile = profileData?.profile || null;
    const classroomOptions = profileData?.classroomOptions || [];
    const videoRef = useRef(null);
    const canvasRef = useRef(null);
    const form = useForm({
        resolver: zodResolver(profileFormSchema),
        defaultValues: {
            firstName: '',
            middleName: '',
            lastName: '',
            mothersName: '',
            email: '',
            contact: '',
            contact2: '',
            medium: '',
            classroom: '',
            gender: '',
            birthday: undefined,
            school: '',
            address: '',
            age: '',
            aadhaarNumber: '',
            bloodGroup: '',
            birthPlace: '',
            motherTongue: '',
            religion: '',
            caste: '',
            subCaste: ''
        },
        mode: 'onSubmit'
    });
    useEffect(()=>{
        const studentToken = localStorage.getItem('studentToken');
        if (!studentToken) {
            toast.error('Please login to access your profile');
            router.push('/');
        }
    }, [
        router
    ]);
    useEffect(()=>{
        const studentToken = localStorage.getItem('studentToken');
        if (studentToken) {
            dispatch(fetchStudentProfile());
        }
    }, [
        dispatch
    ]);
    useEffect(()=>{
        if (isCameraOpen || !profileData) return;
        const profileObj = profileData.profile;
        const studentData = profileObj?.student || JSON.parse(localStorage.getItem('student_data') || '{}');
        const formValues = {
            firstName: studentData?.firstName || '',
            middleName: studentData?.middleName || '',
            lastName: studentData?.lastName || '',
            mothersName: studentData?.mothersName || '',
            email: studentData?.email || '',
            contact: studentData?.contact || '',
            contact2: profileObj?.contactNo2 || '',
            medium: profileObj?.medium || '',
            classroom: profileObj?.classroom || '',
            gender: profileObj?.gender || '',
            birthday: profileObj?.birthday ? new Date(profileObj.birthday) : undefined,
            school: profileObj?.school || '',
            address: profileObj?.address || '',
            age: profileObj?.age?.toString() || '',
            aadhaarNumber: profileObj?.aadhaarNo || '',
            bloodGroup: profileObj?.bloodGroup || '',
            birthPlace: profileObj?.birthPlace || '',
            motherTongue: profileObj?.motherTongue || '',
            religion: profileObj?.religion || '',
            caste: profileObj?.caste || '',
            subCaste: profileObj?.subCaste || ''
        };
        if (profileObj?.photo && !photo) {
            setPhoto(profileObj.photo);
            form.setValue('photo', profileObj.photo);
        }
        if (profileObj?.documentUrl && !uploadedDocument && !isDocumentRemoved) {
            const baseUrl = ("TURBOPACK compile-time value", "http://localhost:4005/") || 'http://localhost:4005/';
            const documentUrl = profileObj.documentUrl.startsWith('http') ? profileObj.documentUrl : `${baseUrl}${profileObj.documentUrl}`;
            const documentObj = {
                name: documentUrl.split('/').pop() || 'Uploaded Document',
                size: 0,
                url: documentUrl,
                type: 'application/octet-stream'
            };
            setUploadedDocument(documentObj);
            form.setValue('document', documentObj);
        }
        const currentValues = form.getValues();
        const isFormEmpty = !currentValues.firstName && !currentValues.lastName && !currentValues.contact;
        const isEducationalDataMissing = !currentValues.medium || !currentValues.classroom;
        if (isFormEmpty || isEducationalDataMissing) {
            form.reset(formValues);
        }
    }, [
        profileData,
        form,
        isCameraOpen,
        photo,
        uploadedDocument,
        isDocumentRemoved
    ]);
    const openCamera = async ()=>{
        setCameraError(null);
        try {
            if (!navigator.mediaDevices?.getUserMedia) {
                throw new Error('Camera not supported on this device');
            }
            setIsCameraOpen(true);
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'user'
                }
            });
            if (videoRef.current) {
                videoRef.current.srcObject = stream;
                videoRef.current.onloadedmetadata = ()=>{
                    videoRef.current?.play().catch(()=>toast.error('Error starting camera preview'));
                };
            }
        } catch (error) {
            setIsCameraOpen(false);
            const message = error.name === 'NotAllowedError' ? 'Please allow camera access in your browser settings.' : 'Could not access camera. Please check your camera settings.';
            setCameraError(message);
            toast.error(message);
        }
    };
    const compressImage = (canvas, maxWidth = 800, quality = 0.6)=>{
        const context = canvas.getContext('2d');
        if (!context) return '';
        const originalWidth = canvas.width;
        const originalHeight = canvas.height;
        let newWidth = originalWidth;
        let newHeight = originalHeight;
        if (originalWidth > maxWidth) {
            newWidth = maxWidth;
            newHeight = originalHeight * maxWidth / originalWidth;
        }
        const compressedCanvas = document.createElement('canvas');
        compressedCanvas.width = newWidth;
        compressedCanvas.height = newHeight;
        const compressedContext = compressedCanvas.getContext('2d');
        if (!compressedContext) return '';
        compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);
        return compressedCanvas.toDataURL('image/jpeg', quality);
    };
    const capturePhoto = ()=>{
        if (!videoRef.current || !canvasRef.current) return;
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const context = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context?.clearRect(0, 0, canvas.width, canvas.height);
        context?.save();
        context?.scale(-1, 1);
        context?.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);
        context?.restore();
        const compressedPhotoDataUrl = compressImage(canvas, 800, 0.6);
        const base64Data = compressedPhotoDataUrl.split(',')[1];
        const sizeInKB = base64Data.length * 3 / 4 / 1024;
        if (sizeInKB > 5120) {
            toast.error('Photo size exceeds 5MB limit. Please try again.');
            return;
        }
        setPhoto(compressedPhotoDataUrl);
        form.setValue('photo', compressedPhotoDataUrl);
        dispatch(updateProfilePhoto(compressedPhotoDataUrl));
        closeCamera();
    };
    const closeCamera = ()=>{
        if (videoRef.current?.srcObject) {
            const stream = videoRef.current.srcObject;
            stream.getTracks().forEach((track)=>track.stop());
            videoRef.current.srcObject = null;
        }
        setIsCameraOpen(false);
        setCameraError(null);
    };
    const removeDocument = ()=>{
        if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {
            URL.revokeObjectURL(uploadedDocument.url);
        }
        setUploadedDocument(null);
        setIsDocumentRemoved(true);
        const fileInput = document.getElementById('document');
        if (fileInput) fileInput.value = '';
        form.setValue('document', null);
    };
    const formatFileSize = (bytes)=>{
        if (bytes < 1024) return bytes + ' bytes';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
        else return (bytes / 1048576).toFixed(1) + ' MB';
    };
    const nextStep = ()=>{
        if (currentStep < steps.length) {
            setCurrentStep(currentStep + 1);
        }
    };
    const prevStep = ()=>{
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };
    const goToStep = (step)=>{
        setCurrentStep(step);
    };
    const isStep1Valid = React.useMemo(()=>{
        return !!(form.getValues().firstName && form.getValues().lastName && form.getValues().contact && form.getValues().birthday && form.getValues().school && form.getValues().address);
    }, [
        form
    ]);
    const isStep2Valid = React.useMemo(()=>{
        return !!(form.getValues().medium && form.getValues().classroom);
    }, [
        form
    ]);
    const isStep3Valid = React.useMemo(()=>{
        const hasPhoto = !!(photo || profileData?.profile?.photo);
        const hasDocument = !!uploadedDocument && !isDocumentRemoved;
        return hasPhoto && hasDocument;
    }, [
        photo,
        uploadedDocument,
        profileData?.profile?.photo,
        isDocumentRemoved
    ]);
    const isFormValid = React.useMemo(()=>{
        return isStep1Valid && isStep2Valid && isStep3Valid;
    }, [
        isStep1Valid,
        isStep2Valid,
        isStep3Valid
    ]);
    const onSubmit = async (data)=>{
        setIsSubmitting(true);
        try {
            const currentPhoto = photo || profileData?.profile?.photo;
            if (!currentPhoto) {
                toast.error('Please capture a photo for your profile');
                setIsSubmitting(false);
                return;
            }
            if (!uploadedDocument || isDocumentRemoved) {
                toast.error('Identity document is required. Please upload a document.');
                setIsSubmitting(false);
                return;
            }
            if (!await form.trigger()) {
                toast.error('Please fill in all required fields correctly');
                setIsSubmitting(false);
                return;
            }
            const jsonData = {
                firstName: data.firstName,
                middleName: data.middleName,
                lastName: data.lastName,
                mothersName: data.mothersName,
                email: data.email,
                contact: data.contact,
                contact2: data.contact2,
                medium: data.medium,
                classroom: data.classroom,
                gender: data.gender,
                birthday: data.birthday?.toISOString() || '',
                school: data.school,
                address: data.address,
                age: data.age,
                aadhaarNumber: data.aadhaarNumber,
                bloodGroup: data.bloodGroup,
                birthPlace: data.birthPlace,
                motherTongue: data.motherTongue,
                religion: data.religion,
                caste: data.caste,
                subCaste: data.subCaste
            };
            if (photo?.startsWith('data:')) {
                const base64Data = photo.split(',')[1];
                const sizeInKB = base64Data.length * 3 / 4 / 1024;
                if (sizeInKB > 5120) {
                    toast.error('Photo size exceeds 5MB limit.');
                    return;
                }
                jsonData.photo = base64Data;
                jsonData.photoMimeType = 'image/jpeg';
            }
            if (uploadedDocument instanceof File || uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {
                const documentFile = uploadedDocument instanceof File ? uploadedDocument : await fetch(uploadedDocument.url).then((res)=>res.blob()).then((blob)=>new File([
                        blob
                    ], uploadedDocument.name, {
                        type: uploadedDocument.type
                    }));
                const documentBase64 = await new Promise((resolve, reject)=>{
                    const reader = new FileReader();
                    reader.onload = ()=>resolve(reader.result.split(',')[1]);
                    reader.onerror = reject;
                    reader.readAsDataURL(documentFile);
                });
                const docSizeKB = documentBase64.length * 3 / 4 / 1024;
                if (docSizeKB > 5120) {
                    toast.error('Document size exceeds 5MB limit.');
                    return;
                }
                jsonData.document = documentBase64;
                jsonData.documentMimeType = documentFile.type;
                jsonData.documentName = documentFile.name;
            }
            // Handle document removal
            if (isDocumentRemoved && profileData?.profile?.documentUrl) {
                jsonData.removeDocument = true;
            }
            const studentToken = localStorage.getItem('studentToken');
            if (!studentToken) {
                toast.error('Please login to submit your profile');
                router.push('/');
                return;
            }
            const result = await dispatch(updateStudentProfile(jsonData));
            if (result.meta.requestStatus === 'fulfilled') {
                toast.success(`Profile ${profile ? 'updated' : 'created'} successfully!`);
                const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');
                const studentData = {
                    ...existingStudentData,
                    id: existingStudentData.id || profileData?.profile?.student?.id || '',
                    firstName: data.firstName,
                    middleName: data.middleName,
                    lastName: data.lastName,
                    mothersName: data.mothersName,
                    email: data.email || existingStudentData.email || profileData?.profile?.student?.email || '',
                    contact: data.contact
                };
                localStorage.setItem('student_data', JSON.stringify(studentData));
                setIsDocumentRemoved(false);
                await dispatch(fetchStudentProfile());
                if (fromQuiz) {
                    if (examId) {
                        router.push(`/uwhiz-exam/${examId}`);
                    } else {
                        router.push('/mock-test');
                    }
                } else {
                    router.push('/');
                }
            } else if (result.meta.requestStatus === 'rejected') {
                const errorMessage = result.payload;
                if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
                    toast.error('Your session has expired. Please login again.');
                    localStorage.removeItem('studentToken');
                    router.push('/');
                } else {
                    toast.error(errorMessage || 'Failed to update profile');
                }
            }
        } catch  {
            toast.error('Failed to submit profile information');
        } finally{
            setIsSubmitting(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Header, {}, void 0, false, {
                fileName: "[project]/src/app/student/profile/page.tsx",
                lineNumber: 493,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen py-12",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white shadow-xl rounded-2xl overflow-hidden",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-black p-6 sm:p-8",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-3xl font-bold text-white",
                                        children: "Student Profile"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                        lineNumber: 498,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-white/80 mt-2",
                                        children: "Complete your profile information"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                        lineNumber: 499,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/student/profile/page.tsx",
                                lineNumber: 497,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gray-50 px-6 py-6 border-b",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "max-w-3xl mx-auto",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: steps.map((step, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center flex-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium cursor-pointer transition-all duration-200 ${currentStep === step.id ? 'bg-black text-white shadow-lg scale-110' : currentStep > step.id ? 'bg-black text-white shadow-md' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'}`,
                                                                    onClick: ()=>goToStep(step.id),
                                                                    children: currentStep > step.id ? '✓' : step.id
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 509,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "ml-4 hidden sm:block",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: `text-sm font-medium transition-colors ${currentStep === step.id ? 'text-black' : 'text-gray-500'}`,
                                                                            children: step.title
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 522,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-gray-400",
                                                                            children: step.description
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 527,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 521,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                            lineNumber: 508,
                                                            columnNumber: 23
                                                        }, this),
                                                        index < steps.length - 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex-1 mx-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `h-1 rounded-full transition-all duration-300 ${currentStep > step.id ? 'bg-black' : 'bg-gray-200'}`
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                lineNumber: 532,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                            lineNumber: 531,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, step.id, true, {
                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                    lineNumber: 507,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                            lineNumber: 505,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex justify-between text-xs text-gray-500 mb-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: "Progress"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                            lineNumber: 544,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: [
                                                                Math.round(currentStep / steps.length * 100),
                                                                "%"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                            lineNumber: 545,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                    lineNumber: 543,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-full bg-gray-200 rounded-full h-2",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "bg-black h-2 rounded-full transition-all duration-300 ease-out",
                                                        style: {
                                                            width: `${currentStep / steps.length * 100}%`
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                        lineNumber: 548,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                    lineNumber: 547,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                            lineNumber: 542,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                    lineNumber: 504,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/student/profile/page.tsx",
                                lineNumber: 503,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-6 sm:p-8",
                                children: profileLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col items-center justify-center py-12",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                            className: "animate-spin h-10 w-10 text-black mb-4",
                                            xmlns: "http://www.w3.org/2000/svg",
                                            fill: "none",
                                            viewBox: "0 0 24 24",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                    className: "opacity-25",
                                                    cx: "12",
                                                    cy: "12",
                                                    r: "10",
                                                    stroke: "currentColor",
                                                    strokeWidth: "4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                    lineNumber: 566,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    className: "opacity-75",
                                                    fill: "currentColor",
                                                    d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                    lineNumber: 567,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                            lineNumber: 560,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-600",
                                            children: "Loading profile information..."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                            lineNumber: 573,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                    lineNumber: 559,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Form, {
                                    ...form,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                        onSubmit: form.handleSubmit(onSubmit),
                                        className: "space-y-8",
                                        children: [
                                            currentStep === 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-6 animate-in slide-in-from-right-5 duration-300",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Card, {
                                                        className: "shadow-lg border-0",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardHeader, {
                                                                className: "bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardTitle, {
                                                                        className: "text-xl font-semibold text-gray-800 flex items-center gap-2",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "w-8 h-8 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold",
                                                                                children: "1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 585,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            "Personal Information"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 584,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardDescription, {
                                                                        className: "text-gray-600",
                                                                        children: "Enter your basic personal details and contact information"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 590,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                lineNumber: 583,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardContent, {
                                                                className: "space-y-6 p-8",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "firstName",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "First Name *"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 601,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter First Name"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 603,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 602,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 609,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 600,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 596,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "middleName",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Middle Name"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 618,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Middle Name"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 620,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 619,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 626,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 617,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 613,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "lastName",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Last Name *"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 635,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Last Name"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 637,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 636,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 643,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 634,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 630,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "mothersName",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Mother's Name"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 652,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Mother's Name"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 654,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 653,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 660,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 651,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 647,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 595,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                        control: form.control,
                                                                        name: "email",
                                                                        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                        className: "text-black font-medium",
                                                                                        children: "Email"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 670,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                            ...field,
                                                                                            className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                            placeholder: "Enter Email",
                                                                                            type: "email"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 672,
                                                                                            columnNumber: 33
                                                                                        }, void 0)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 671,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                        className: "text-red-500"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 679,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 669,
                                                                                columnNumber: 29
                                                                            }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 665,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "contact",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Contact Number *"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 689,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "8520369851",
                                                                                                    type: "tel",
                                                                                                    inputMode: "numeric",
                                                                                                    pattern: "[0-9]*",
                                                                                                    onKeyDown: (e)=>{
                                                                                                        const specialKeys = [
                                                                                                            'Backspace',
                                                                                                            'Tab',
                                                                                                            'Enter',
                                                                                                            'Escape',
                                                                                                            'Delete',
                                                                                                            'ArrowLeft',
                                                                                                            'ArrowRight',
                                                                                                            'Home',
                                                                                                            'End'
                                                                                                        ];
                                                                                                        if (specialKeys.includes(e.key)) {
                                                                                                            return;
                                                                                                        }
                                                                                                        if (e.ctrlKey && [
                                                                                                            'a',
                                                                                                            'c',
                                                                                                            'v',
                                                                                                            'x'
                                                                                                        ].includes(e.key.toLowerCase())) {
                                                                                                            return;
                                                                                                        }
                                                                                                        if (!/^\d$/.test(e.key)) {
                                                                                                            e.preventDefault();
                                                                                                        }
                                                                                                    },
                                                                                                    onChange: (e)=>{
                                                                                                        const value = e.target.value.replace(/\D/g, '');
                                                                                                        field.onChange(value);
                                                                                                    }
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 691,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 690,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 726,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 688,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 684,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "contact2",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Contact Number 2"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 735,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Alternate Number",
                                                                                                    type: "tel",
                                                                                                    inputMode: "numeric",
                                                                                                    pattern: "[0-9]*",
                                                                                                    onKeyDown: (e)=>{
                                                                                                        const specialKeys = [
                                                                                                            'Backspace',
                                                                                                            'Tab',
                                                                                                            'Enter',
                                                                                                            'Escape',
                                                                                                            'Delete',
                                                                                                            'ArrowLeft',
                                                                                                            'ArrowRight',
                                                                                                            'Home',
                                                                                                            'End'
                                                                                                        ];
                                                                                                        if (specialKeys.includes(e.key)) {
                                                                                                            return;
                                                                                                        }
                                                                                                        if (e.ctrlKey && [
                                                                                                            'a',
                                                                                                            'c',
                                                                                                            'v',
                                                                                                            'x'
                                                                                                        ].includes(e.key.toLowerCase())) {
                                                                                                            return;
                                                                                                        }
                                                                                                        if (!/^\d$/.test(e.key)) {
                                                                                                            e.preventDefault();
                                                                                                        }
                                                                                                    },
                                                                                                    onChange: (e)=>{
                                                                                                        const value = e.target.value.replace(/\D/g, '');
                                                                                                        field.onChange(value);
                                                                                                    }
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 737,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 736,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 772,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 734,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 730,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 683,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-1 md:grid-cols-3 gap-6",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "gender",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Gender"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 783,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Select, {
                                                                                                onValueChange: field.onChange,
                                                                                                value: field.value || undefined,
                                                                                                children: [
                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectTrigger, {
                                                                                                            className: "bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",
                                                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectValue, {
                                                                                                                placeholder: "Select"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 790,
                                                                                                                columnNumber: 39
                                                                                                            }, void 0)
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 789,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0)
                                                                                                    }, void 0, false, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 788,
                                                                                                        columnNumber: 35
                                                                                                    }, void 0),
                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectContent, {
                                                                                                        className: "bg-white w-[var(--radix-select-trigger-width)]",
                                                                                                        children: [
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "male",
                                                                                                                children: "Male"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 794,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "female",
                                                                                                                children: "Female"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 795,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "other",
                                                                                                                children: "Other"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 796,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0)
                                                                                                        ]
                                                                                                    }, void 0, true, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 793,
                                                                                                        columnNumber: 35
                                                                                                    }, void 0)
                                                                                                ]
                                                                                            }, void 0, true, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 784,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 799,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 782,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 778,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "age",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Age"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 808,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Age",
                                                                                                    type: "number",
                                                                                                    min: "1",
                                                                                                    max: "100"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 810,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 809,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 819,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 807,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 803,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "birthday",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        className: "flex flex-col",
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Date of Birth"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 828,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Popover, {
                                                                                                children: [
                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(PopoverTrigger, {
                                                                                                        asChild: true,
                                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                                                                variant: "outline",
                                                                                                                className: cn('w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg', !field.value && 'text-muted-foreground'),
                                                                                                                children: [
                                                                                                                    field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? format(field.value, 'PPP') : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                                                        children: "Select your birthday"
                                                                                                                    }, void 0, false, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 842,
                                                                                                                        columnNumber: 41
                                                                                                                    }, void 0),
                                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CalendarIcon, {
                                                                                                                        className: "ml-auto h-4 w-4 opacity-50"
                                                                                                                    }, void 0, false, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 844,
                                                                                                                        columnNumber: 39
                                                                                                                    }, void 0)
                                                                                                                ]
                                                                                                            }, void 0, true, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 832,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0)
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 831,
                                                                                                            columnNumber: 35
                                                                                                        }, void 0)
                                                                                                    }, void 0, false, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 830,
                                                                                                        columnNumber: 33
                                                                                                    }, void 0),
                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(PopoverContent, {
                                                                                                        className: "w-auto p-0 bg-white border border-gray-300 shadow-lg",
                                                                                                        align: "start",
                                                                                                        children: [
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                className: "p-3 border-b border-gray-200",
                                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                    className: "flex gap-2 mb-3",
                                                                                                                    children: [
                                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Select, {
                                                                                                                            value: field.value ? field.value.getFullYear().toString() : "",
                                                                                                                            onValueChange: (year)=>{
                                                                                                                                const currentDate = field.value || new Date();
                                                                                                                                const newDate = new Date(currentDate);
                                                                                                                                newDate.setFullYear(parseInt(year));
                                                                                                                                field.onChange(newDate);
                                                                                                                            },
                                                                                                                            children: [
                                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectTrigger, {
                                                                                                                                    className: "w-24",
                                                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectValue, {
                                                                                                                                        placeholder: "Year"
                                                                                                                                    }, void 0, false, {
                                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                        lineNumber: 861,
                                                                                                                                        columnNumber: 43
                                                                                                                                    }, void 0)
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 860,
                                                                                                                                    columnNumber: 41
                                                                                                                                }, void 0),
                                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectContent, {
                                                                                                                                    className: "max-h-48",
                                                                                                                                    children: Array.from({
                                                                                                                                        length: 125
                                                                                                                                    }, (_, i)=>{
                                                                                                                                        const year = new Date().getFullYear() - i;
                                                                                                                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                                            value: year.toString(),
                                                                                                                                            children: year
                                                                                                                                        }, year, false, {
                                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                            lineNumber: 867,
                                                                                                                                            columnNumber: 47
                                                                                                                                        }, void 0);
                                                                                                                                    })
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 863,
                                                                                                                                    columnNumber: 41
                                                                                                                                }, void 0)
                                                                                                                            ]
                                                                                                                        }, void 0, true, {
                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                            lineNumber: 851,
                                                                                                                            columnNumber: 39
                                                                                                                        }, void 0),
                                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Select, {
                                                                                                                            value: field.value ? field.value.getMonth().toString() : "",
                                                                                                                            onValueChange: (month)=>{
                                                                                                                                const currentDate = field.value || new Date();
                                                                                                                                const newDate = new Date(currentDate);
                                                                                                                                newDate.setMonth(parseInt(month));
                                                                                                                                field.onChange(newDate);
                                                                                                                            },
                                                                                                                            children: [
                                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectTrigger, {
                                                                                                                                    className: "w-32",
                                                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectValue, {
                                                                                                                                        placeholder: "Month"
                                                                                                                                    }, void 0, false, {
                                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                        lineNumber: 884,
                                                                                                                                        columnNumber: 43
                                                                                                                                    }, void 0)
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 883,
                                                                                                                                    columnNumber: 41
                                                                                                                                }, void 0),
                                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectContent, {
                                                                                                                                    children: [
                                                                                                                                        'January',
                                                                                                                                        'February',
                                                                                                                                        'March',
                                                                                                                                        'April',
                                                                                                                                        'May',
                                                                                                                                        'June',
                                                                                                                                        'July',
                                                                                                                                        'August',
                                                                                                                                        'September',
                                                                                                                                        'October',
                                                                                                                                        'November',
                                                                                                                                        'December'
                                                                                                                                    ].map((month, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                                            value: index.toString(),
                                                                                                                                            children: month
                                                                                                                                        }, index, false, {
                                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                            lineNumber: 891,
                                                                                                                                            columnNumber: 45
                                                                                                                                        }, void 0))
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 886,
                                                                                                                                    columnNumber: 41
                                                                                                                                }, void 0)
                                                                                                                            ]
                                                                                                                        }, void 0, true, {
                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                            lineNumber: 874,
                                                                                                                            columnNumber: 39
                                                                                                                        }, void 0)
                                                                                                                    ]
                                                                                                                }, void 0, true, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 850,
                                                                                                                    columnNumber: 37
                                                                                                                }, void 0)
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 849,
                                                                                                                columnNumber: 35
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Calendar, {
                                                                                                                mode: "single",
                                                                                                                selected: field.value,
                                                                                                                onSelect: field.onChange,
                                                                                                                disabled: (date)=>date > new Date() || date < new Date('1900-01-01'),
                                                                                                                month: field.value || new Date(),
                                                                                                                className: "rounded-md border-0"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 899,
                                                                                                                columnNumber: 35
                                                                                                            }, void 0)
                                                                                                        ]
                                                                                                    }, void 0, true, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 848,
                                                                                                        columnNumber: 33
                                                                                                    }, void 0)
                                                                                                ]
                                                                                            }, void 0, true, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 829,
                                                                                                columnNumber: 31
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormDescription, {
                                                                                                className: "text-xs text-gray-500",
                                                                                                children: "Your date of birth will be verified with your documents"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 909,
                                                                                                columnNumber: 31
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 912,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 827,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 823,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 777,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                        control: form.control,
                                                                        name: "address",
                                                                        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                        className: "text-black font-medium",
                                                                                        children: "Address"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 922,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Textarea, {
                                                                                            ...field,
                                                                                            rows: 3,
                                                                                            className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none",
                                                                                            placeholder: "Enter your full address"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 924,
                                                                                            columnNumber: 33
                                                                                        }, void 0)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 923,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormDescription, {
                                                                                        className: "text-xs text-gray-500",
                                                                                        children: "Provide your complete residential address"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 931,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                        className: "text-red-500"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 934,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 921,
                                                                                columnNumber: 29
                                                                            }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 917,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                        control: form.control,
                                                                        name: "school",
                                                                        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                        className: "text-black font-medium",
                                                                                        children: "School Name *"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 943,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                            ...field,
                                                                                            className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                            placeholder: "Enter School"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 945,
                                                                                            columnNumber: 33
                                                                                        }, void 0)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 944,
                                                                                        columnNumber: 31
                                                                                    }, void 0),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                        className: "text-red-500"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 951,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 942,
                                                                                columnNumber: 29
                                                                            }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 938,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "aadhaarNumber",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Aadhaar Number"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 961,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Aadhaar No",
                                                                                                    type: "tel",
                                                                                                    inputMode: "numeric",
                                                                                                    pattern: "[0-9]*",
                                                                                                    maxLength: 12,
                                                                                                    onKeyDown: (e)=>{
                                                                                                        const specialKeys = [
                                                                                                            'Backspace',
                                                                                                            'Tab',
                                                                                                            'Enter',
                                                                                                            'Escape',
                                                                                                            'Delete',
                                                                                                            'ArrowLeft',
                                                                                                            'ArrowRight',
                                                                                                            'Home',
                                                                                                            'End'
                                                                                                        ];
                                                                                                        if (specialKeys.includes(e.key)) {
                                                                                                            return;
                                                                                                        }
                                                                                                        if (e.ctrlKey && [
                                                                                                            'a',
                                                                                                            'c',
                                                                                                            'v',
                                                                                                            'x'
                                                                                                        ].includes(e.key.toLowerCase())) {
                                                                                                            return;
                                                                                                        }
                                                                                                        if (!/^\d$/.test(e.key)) {
                                                                                                            e.preventDefault();
                                                                                                        }
                                                                                                    },
                                                                                                    onChange: (e)=>{
                                                                                                        const value = e.target.value.replace(/\D/g, '');
                                                                                                        field.onChange(value);
                                                                                                    }
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 963,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 962,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 999,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 960,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 956,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "bloodGroup",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Blood Group"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1008,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Select, {
                                                                                                onValueChange: field.onChange,
                                                                                                value: field.value || undefined,
                                                                                                children: [
                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectTrigger, {
                                                                                                            className: "bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",
                                                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectValue, {
                                                                                                                placeholder: "Select"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1015,
                                                                                                                columnNumber: 39
                                                                                                            }, void 0)
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1014,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0)
                                                                                                    }, void 0, false, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 1013,
                                                                                                        columnNumber: 35
                                                                                                    }, void 0),
                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectContent, {
                                                                                                        className: "bg-white w-[var(--radix-select-trigger-width)]",
                                                                                                        children: [
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "A+",
                                                                                                                children: "A+"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1019,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "A-",
                                                                                                                children: "A-"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1020,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "B+",
                                                                                                                children: "B+"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1021,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "B-",
                                                                                                                children: "B-"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1022,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "AB+",
                                                                                                                children: "AB+"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1023,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "AB-",
                                                                                                                children: "AB-"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1024,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "O+",
                                                                                                                children: "O+"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1025,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                                value: "O-",
                                                                                                                children: "O-"
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1026,
                                                                                                                columnNumber: 37
                                                                                                            }, void 0)
                                                                                                        ]
                                                                                                    }, void 0, true, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 1018,
                                                                                                        columnNumber: 35
                                                                                                    }, void 0)
                                                                                                ]
                                                                                            }, void 0, true, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1009,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1029,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1007,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1003,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "birthPlace",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Birth Place"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1038,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Birth Place"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1040,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1039,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1046,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1037,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1033,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "motherTongue",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Mother Tongue"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1055,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Mother Tongue"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1057,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1056,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1063,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1054,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1050,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "religion",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Religion"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1072,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Religion"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1074,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1073,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1080,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1071,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1067,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "caste",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Caste"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1089,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Caste"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1091,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1090,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1097,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1088,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1084,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                                control: form.control,
                                                                                name: "subCaste",
                                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                                className: "text-black font-medium",
                                                                                                children: "Sub Caste"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1106,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                    ...field,
                                                                                                    className: "bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg",
                                                                                                    placeholder: "Enter Sub Caste"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1108,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1107,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1114,
                                                                                                columnNumber: 33
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1105,
                                                                                        columnNumber: 31
                                                                                    }, void 0)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1101,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 955,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                lineNumber: 594,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                        lineNumber: 582,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-end pt-6 border-t border-gray-100",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                            type: "button",
                                                            onClick: nextStep,
                                                            disabled: !isStep1Valid,
                                                            className: "bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200",
                                                            children: "Next Step →"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                            lineNumber: 1124,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                        lineNumber: 1123,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                lineNumber: 581,
                                                columnNumber: 23
                                            }, this),
                                            currentStep === 2 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-6 animate-in slide-in-from-right-5 duration-300",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Card, {
                                                        className: "shadow-lg border-0",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardHeader, {
                                                                className: "bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-lg",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardTitle, {
                                                                        className: "text-xl font-semibold text-gray-800 flex items-center gap-2",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "w-8 h-8 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold",
                                                                                children: "2"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1142,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            "Educational Information"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 1141,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardDescription, {
                                                                        className: "text-gray-600",
                                                                        children: "Select your medium of instruction and classroom standard"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                        lineNumber: 1147,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                lineNumber: 1140,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardContent, {
                                                                className: "space-y-6 p-8",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                            control: form.control,
                                                                            name: "medium",
                                                                            render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                            className: "text-black font-medium",
                                                                                            children: "Medium *"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1158,
                                                                                            columnNumber: 33
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Select, {
                                                                                            onValueChange: (value)=>{
                                                                                                field.onChange(value);
                                                                                            },
                                                                                            value: field.value || undefined,
                                                                                            children: [
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectTrigger, {
                                                                                                        className: "bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",
                                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectValue, {
                                                                                                            placeholder: "Select"
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1167,
                                                                                                            columnNumber: 39
                                                                                                        }, void 0)
                                                                                                    }, void 0, false, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 1166,
                                                                                                        columnNumber: 37
                                                                                                    }, void 0)
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1165,
                                                                                                    columnNumber: 35
                                                                                                }, void 0),
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectContent, {
                                                                                                    className: "bg-white w-[var(--radix-select-trigger-width)]",
                                                                                                    children: [
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                            value: "english",
                                                                                                            children: "English"
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1171,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0),
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                            value: "gujarati",
                                                                                                            children: "Gujarati"
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1172,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0)
                                                                                                    ]
                                                                                                }, void 0, true, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1170,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1159,
                                                                                            columnNumber: 33
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                            className: "text-red-500"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1175,
                                                                                            columnNumber: 33
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                    lineNumber: 1157,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 1153,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                            control: form.control,
                                                                            name: "classroom",
                                                                            render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormLabel, {
                                                                                            className: "text-black font-medium",
                                                                                            children: "Standard *"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1184,
                                                                                            columnNumber: 33
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Select, {
                                                                                            onValueChange: (value)=>{
                                                                                                field.onChange(value);
                                                                                            },
                                                                                            value: field.value || undefined,
                                                                                            children: [
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectTrigger, {
                                                                                                        className: "bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full",
                                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectValue, {
                                                                                                            placeholder: "Select"
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1193,
                                                                                                            columnNumber: 39
                                                                                                        }, void 0)
                                                                                                    }, void 0, false, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 1192,
                                                                                                        columnNumber: 37
                                                                                                    }, void 0)
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1191,
                                                                                                    columnNumber: 35
                                                                                                }, void 0),
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectContent, {
                                                                                                    className: "bg-white w-[var(--radix-select-trigger-width)]",
                                                                                                    children: profileLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                        className: "flex items-center justify-center p-4",
                                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                                                            className: "animate-spin h-5 w-5 text-black",
                                                                                                            xmlns: "http://www.w3.org/2000/svg",
                                                                                                            fill: "none",
                                                                                                            viewBox: "0 0 24 24",
                                                                                                            children: [
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                                                                                    className: "opacity-25",
                                                                                                                    cx: "12",
                                                                                                                    cy: "12",
                                                                                                                    r: "10",
                                                                                                                    stroke: "currentColor",
                                                                                                                    strokeWidth: "4"
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1205,
                                                                                                                    columnNumber: 43
                                                                                                                }, void 0),
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                                                    className: "opacity-75",
                                                                                                                    fill: "currentColor",
                                                                                                                    d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1213,
                                                                                                                    columnNumber: 43
                                                                                                                }, void 0)
                                                                                                            ]
                                                                                                        }, void 0, true, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1199,
                                                                                                            columnNumber: 41
                                                                                                        }, void 0)
                                                                                                    }, void 0, false, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 1198,
                                                                                                        columnNumber: 39
                                                                                                    }, void 0) : classroomOptions.length > 0 ? classroomOptions.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(SelectItem, {
                                                                                                            value: option.value,
                                                                                                            children: option.value
                                                                                                        }, option.id, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1222,
                                                                                                            columnNumber: 41
                                                                                                        }, void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                        className: "p-2 text-center text-gray-500",
                                                                                                        children: "No classroom options available"
                                                                                                    }, void 0, false, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 1227,
                                                                                                        columnNumber: 39
                                                                                                    }, void 0)
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1196,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1185,
                                                                                            columnNumber: 33
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                            className: "text-red-500"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1231,
                                                                                            columnNumber: 33
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                    lineNumber: 1183,
                                                                                    columnNumber: 31
                                                                                }, void 0)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 1179,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 1152,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                lineNumber: 1151,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                        lineNumber: 1139,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between pt-6 border-t border-gray-100",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                type: "button",
                                                                onClick: prevStep,
                                                                variant: "outline",
                                                                className: "border-gray-300 hover:bg-gray-50 px-8 py-3 font-medium transition-all duration-200",
                                                                children: "← Previous"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                lineNumber: 1241,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                type: "button",
                                                                onClick: nextStep,
                                                                disabled: !isStep2Valid,
                                                                className: "bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200",
                                                                children: "Next Step →"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                lineNumber: 1249,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                        lineNumber: 1240,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                lineNumber: 1138,
                                                columnNumber: 23
                                            }, this),
                                            currentStep === 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-6 animate-in slide-in-from-right-5 duration-300",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Card, {
                                                    className: "shadow-lg border-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardHeader, {
                                                            className: "bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardTitle, {
                                                                    className: "text-xl font-semibold text-gray-800 flex items-center gap-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "w-8 h-8 bg-black text-white rounded-full flex items-center justify-center text-sm font-bold",
                                                                            children: "3"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 1267,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        "Documents & Photo"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 1266,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardDescription, {
                                                                    className: "text-gray-600",
                                                                    children: "Upload your profile photo and identity document"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 1272,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                            lineNumber: 1265,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardContent, {
                                                            className: "space-y-6 p-8",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                    control: form.control,
                                                                    name: "photo",
                                                                    render: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Card, {
                                                                            className: "shadow-lg border-0",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardHeader, {
                                                                                    className: "bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardTitle, {
                                                                                            className: "text-lg font-medium text-gray-800",
                                                                                            children: "Student Image"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1284,
                                                                                            columnNumber: 33
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardDescription, {
                                                                                            className: "text-gray-600",
                                                                                            children: "Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1285,
                                                                                            columnNumber: 33
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                    lineNumber: 1283,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardContent, {
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                    children: [
                                                                                                        cameraError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                            className: "mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",
                                                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                                                className: "text-red-700 text-sm",
                                                                                                                children: cameraError
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1295,
                                                                                                                columnNumber: 39
                                                                                                            }, void 0)
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1294,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0),
                                                                                                        !isCameraOpen && !photo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                                                            type: "button",
                                                                                                            onClick: openCamera,
                                                                                                            className: "w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2",
                                                                                                            children: [
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Camera, {
                                                                                                                    className: "h-5 w-5 mr-2"
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1304,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0),
                                                                                                                "Open Camera"
                                                                                                            ]
                                                                                                        }, void 0, true, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1299,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0),
                                                                                                        isCameraOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                            className: "camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm",
                                                                                                            children: [
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                                                                                                    ref: videoRef,
                                                                                                                    autoPlay: true,
                                                                                                                    playsInline: true,
                                                                                                                    className: "w-full h-auto transform scale-x-[-1]"
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1310,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0),
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                    className: "flex p-4 bg-gray-50",
                                                                                                                    children: [
                                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                                                                            type: "button",
                                                                                                                            onClick: capturePhoto,
                                                                                                                            variant: "default",
                                                                                                                            className: "flex-1 mr-2 bg-black hover:bg-gray-800 text-white",
                                                                                                                            children: [
                                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Check, {
                                                                                                                                    className: "h-4 w-4 mr-2"
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 1323,
                                                                                                                                    columnNumber: 43
                                                                                                                                }, void 0),
                                                                                                                                "Capture"
                                                                                                                            ]
                                                                                                                        }, void 0, true, {
                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                            lineNumber: 1317,
                                                                                                                            columnNumber: 41
                                                                                                                        }, void 0),
                                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                                                                            type: "button",
                                                                                                                            onClick: closeCamera,
                                                                                                                            variant: "outline",
                                                                                                                            className: "flex-1 border-gray-300",
                                                                                                                            children: [
                                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(X, {
                                                                                                                                    className: "h-4 w-4 mr-2"
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 1332,
                                                                                                                                    columnNumber: 43
                                                                                                                                }, void 0),
                                                                                                                                "Cancel"
                                                                                                                            ]
                                                                                                                        }, void 0, true, {
                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                            lineNumber: 1326,
                                                                                                                            columnNumber: 41
                                                                                                                        }, void 0)
                                                                                                                    ]
                                                                                                                }, void 0, true, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1316,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0)
                                                                                                            ]
                                                                                                        }, void 0, true, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1309,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0),
                                                                                                        !isCameraOpen && (profileData?.profile?.photo || photo) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                            className: "flex flex-col sm:flex-row items-center gap-4",
                                                                                                            children: [
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                    className: "border rounded-lg shadow-md bg-gray-50 p-4 max-w-full",
                                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                        className: "flex justify-center",
                                                                                                                        children: (()=>{
                                                                                                                            const displayPhoto = photo || profileData?.profile?.photo;
                                                                                                                            if (displayPhoto) {
                                                                                                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Image, {
                                                                                                                                    src: displayPhoto.startsWith('data:') ? displayPhoto : displayPhoto.startsWith('http') ? displayPhoto : `${("TURBOPACK compile-time value", "http://localhost:4005/") || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`,
                                                                                                                                    alt: "Student Photo",
                                                                                                                                    height: 1000,
                                                                                                                                    width: 1000,
                                                                                                                                    className: "max-w-full max-h-80 object-contain rounded-lg",
                                                                                                                                    style: {
                                                                                                                                        height: 'auto',
                                                                                                                                        width: 'auto'
                                                                                                                                    },
                                                                                                                                    unoptimized: displayPhoto.startsWith('data:')
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 1346,
                                                                                                                                    columnNumber: 49
                                                                                                                                }, void 0);
                                                                                                                            }
                                                                                                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                                className: "flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg",
                                                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Camera, {
                                                                                                                                    className: "h-12 w-12 text-gray-400"
                                                                                                                                }, void 0, false, {
                                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                    lineNumber: 1365,
                                                                                                                                    columnNumber: 49
                                                                                                                                }, void 0)
                                                                                                                            }, void 0, false, {
                                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                lineNumber: 1364,
                                                                                                                                columnNumber: 47
                                                                                                                            }, void 0);
                                                                                                                        })()
                                                                                                                    }, void 0, false, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 1341,
                                                                                                                        columnNumber: 41
                                                                                                                    }, void 0)
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1340,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0),
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                                                                    type: "button",
                                                                                                                    onClick: ()=>{
                                                                                                                        setPhoto(null);
                                                                                                                        setCameraError(null);
                                                                                                                        dispatch(updateProfilePhoto(undefined));
                                                                                                                        form.setValue('photo', null);
                                                                                                                        openCamera();
                                                                                                                    },
                                                                                                                    variant: "outline",
                                                                                                                    className: "border-gray-300",
                                                                                                                    children: [
                                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Camera, {
                                                                                                                            className: "h-4 w-4 mr-2"
                                                                                                                        }, void 0, false, {
                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                            lineNumber: 1383,
                                                                                                                            columnNumber: 41
                                                                                                                        }, void 0),
                                                                                                                        "Retake Photo"
                                                                                                                    ]
                                                                                                                }, void 0, true, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1371,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0)
                                                                                                            ]
                                                                                                        }, void 0, true, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1339,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0),
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                                                                                                            ref: canvasRef,
                                                                                                            style: {
                                                                                                                display: 'none'
                                                                                                            }
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1388,
                                                                                                            columnNumber: 35
                                                                                                        }, void 0)
                                                                                                    ]
                                                                                                }, void 0, true, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1292,
                                                                                                    columnNumber: 33
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1291,
                                                                                                columnNumber: 31
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormDescription, {
                                                                                                className: "text-xs text-gray-500 mt-2",
                                                                                                children: "A clear photo helps us identify you and personalize your profile"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1391,
                                                                                                columnNumber: 31
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1394,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1290,
                                                                                        columnNumber: 29
                                                                                    }, void 0)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                    lineNumber: 1289,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 1282,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 1278,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                                    control: form.control,
                                                                    name: "document",
                                                                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Card, {
                                                                            className: "shadow-lg border-0",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardHeader, {
                                                                                    className: "bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardTitle, {
                                                                                            className: "text-lg font-medium text-gray-800",
                                                                                            children: "Identity Document"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1406,
                                                                                            columnNumber: 29
                                                                                        }, void 0),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardDescription, {
                                                                                            className: "text-gray-600",
                                                                                            children: "Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                            lineNumber: 1407,
                                                                                            columnNumber: 29
                                                                                        }, void 0)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                    lineNumber: 1405,
                                                                                    columnNumber: 27
                                                                                }, void 0),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(CardContent, {
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormItem, {
                                                                                        children: [
                                                                                            !uploadedDocument ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormControl, {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                    className: "flex items-center justify-center w-full",
                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                                        className: "flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors",
                                                                                                        children: [
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                className: "flex flex-col items-center justify-center pt-5 pb-6",
                                                                                                                children: [
                                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Upload, {
                                                                                                                        className: "w-10 h-10 mb-3 text-black"
                                                                                                                    }, void 0, false, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 1418,
                                                                                                                        columnNumber: 41
                                                                                                                    }, void 0),
                                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                                                        className: "mb-2 text-sm text-gray-700",
                                                                                                                        children: [
                                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                                                                className: "font-semibold",
                                                                                                                                children: "Click to upload"
                                                                                                                            }, void 0, false, {
                                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                                lineNumber: 1420,
                                                                                                                                columnNumber: 43
                                                                                                                            }, void 0),
                                                                                                                            " or drag and drop"
                                                                                                                        ]
                                                                                                                    }, void 0, true, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 1419,
                                                                                                                        columnNumber: 41
                                                                                                                    }, void 0),
                                                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                                                        className: "text-xs text-gray-500",
                                                                                                                        children: "PDF, PNG, JPG or JPEG (MAX. 5MB)"
                                                                                                                    }, void 0, false, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 1422,
                                                                                                                        columnNumber: 41
                                                                                                                    }, void 0)
                                                                                                                ]
                                                                                                            }, void 0, true, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1417,
                                                                                                                columnNumber: 39
                                                                                                            }, void 0),
                                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Input, {
                                                                                                                id: "document",
                                                                                                                type: "file",
                                                                                                                accept: ".pdf,.jpg,.jpeg,.png",
                                                                                                                className: "hidden",
                                                                                                                onChange: (e)=>{
                                                                                                                    const file = e.target.files?.[0];
                                                                                                                    if (file) {
                                                                                                                        if (file.size > 5 * 1024 * 1024) {
                                                                                                                            toast.error('File size exceeds 5MB limit');
                                                                                                                            return;
                                                                                                                        }
                                                                                                                        const documentWithUrl = {
                                                                                                                            name: file.name,
                                                                                                                            size: file.size,
                                                                                                                            type: file.type,
                                                                                                                            url: URL.createObjectURL(file)
                                                                                                                        };
                                                                                                                        setUploadedDocument(documentWithUrl);
                                                                                                                        setIsDocumentRemoved(false);
                                                                                                                        field.onChange(file);
                                                                                                                    }
                                                                                                                }
                                                                                                            }, void 0, false, {
                                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                lineNumber: 1424,
                                                                                                                columnNumber: 39
                                                                                                            }, void 0)
                                                                                                        ]
                                                                                                    }, void 0, true, {
                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                        lineNumber: 1416,
                                                                                                        columnNumber: 37
                                                                                                    }, void 0)
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1415,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1414,
                                                                                                columnNumber: 33
                                                                                            }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                className: "bg-gray-50 rounded-lg p-4 border border-gray-200",
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                    className: "flex items-center justify-between",
                                                                                                    children: [
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                            className: "flex items-center space-x-3",
                                                                                                            children: [
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                    className: "p-2 bg-[#fff8f3] rounded-full",
                                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FileText, {
                                                                                                                        className: "h-5 w-5 text-black"
                                                                                                                    }, void 0, false, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 1456,
                                                                                                                        columnNumber: 41
                                                                                                                    }, void 0)
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1455,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0),
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                                    children: [
                                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                                                            className: "text-sm font-medium text-gray-700",
                                                                                                                            children: uploadedDocument.name
                                                                                                                        }, void 0, false, {
                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                            lineNumber: 1459,
                                                                                                                            columnNumber: 41
                                                                                                                        }, void 0),
                                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                                                            className: "text-xs text-gray-500",
                                                                                                                            children: uploadedDocument instanceof File ? formatFileSize(uploadedDocument.size) : 'Previously uploaded document'
                                                                                                                        }, void 0, false, {
                                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                            lineNumber: 1460,
                                                                                                                            columnNumber: 41
                                                                                                                        }, void 0)
                                                                                                                    ]
                                                                                                                }, void 0, true, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1458,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0)
                                                                                                            ]
                                                                                                        }, void 0, true, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1454,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0),
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                            className: "flex space-x-2",
                                                                                                            children: [
                                                                                                                uploadedDocument && 'url' in uploadedDocument && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                                                                    type: "button",
                                                                                                                    variant: "outline",
                                                                                                                    size: "sm",
                                                                                                                    onClick: ()=>window.open(uploadedDocument.url, '_blank'),
                                                                                                                    className: "h-8 px-3 border-gray-200",
                                                                                                                    children: "View"
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1469,
                                                                                                                    columnNumber: 41
                                                                                                                }, void 0),
                                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                                                                    type: "button",
                                                                                                                    variant: "outline",
                                                                                                                    size: "sm",
                                                                                                                    onClick: removeDocument,
                                                                                                                    className: "h-8 w-8 p-0 border-gray-200",
                                                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(X, {
                                                                                                                        className: "h-4 w-4 text-gray-500"
                                                                                                                    }, void 0, false, {
                                                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                        lineNumber: 1486,
                                                                                                                        columnNumber: 41
                                                                                                                    }, void 0)
                                                                                                                }, void 0, false, {
                                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                                    lineNumber: 1479,
                                                                                                                    columnNumber: 39
                                                                                                                }, void 0)
                                                                                                            ]
                                                                                                        }, void 0, true, {
                                                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                            lineNumber: 1467,
                                                                                                            columnNumber: 37
                                                                                                        }, void 0)
                                                                                                    ]
                                                                                                }, void 0, true, {
                                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                    lineNumber: 1453,
                                                                                                    columnNumber: 35
                                                                                                }, void 0)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1452,
                                                                                                columnNumber: 33
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormDescription, {
                                                                                                className: "text-xs text-gray-500 mt-2",
                                                                                                children: "This document will serve to verify your identity and date of birth."
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1492,
                                                                                                columnNumber: 31
                                                                                            }, void 0),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(FormMessage, {
                                                                                                className: "text-red-500"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1495,
                                                                                                columnNumber: 31
                                                                                            }, void 0)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1412,
                                                                                        columnNumber: 29
                                                                                    }, void 0)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                    lineNumber: 1411,
                                                                                    columnNumber: 27
                                                                                }, void 0)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 1404,
                                                                            columnNumber: 25
                                                                        }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 1400,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex justify-between pt-8 border-t border-gray-100",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                            type: "button",
                                                                            onClick: prevStep,
                                                                            variant: "outline",
                                                                            className: "border-gray-300 hover:bg-gray-50 px-8 py-3 font-medium transition-all duration-200",
                                                                            children: "← Previous"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 1503,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Button, {
                                                                            type: "submit",
                                                                            className: `font-medium py-3 px-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${isFormValid && !isSubmitting ? 'bg-black text-white hover:bg-gray-800' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,
                                                                            disabled: isSubmitting || !isFormValid,
                                                                            children: isSubmitting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "flex items-center justify-center gap-2",
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                                        className: "animate-spin -ml-1 mr-2 h-5 w-5 text-white",
                                                                                        xmlns: "http://www.w3.org/2000/svg",
                                                                                        fill: "none",
                                                                                        viewBox: "0 0 24 24",
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                                                                className: "opacity-25",
                                                                                                cx: "12",
                                                                                                cy: "12",
                                                                                                r: "10",
                                                                                                stroke: "currentColor",
                                                                                                strokeWidth: "4"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1528,
                                                                                                columnNumber: 35
                                                                                            }, this),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                                className: "opacity-75",
                                                                                                fill: "currentColor",
                                                                                                d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                                lineNumber: 1536,
                                                                                                columnNumber: 35
                                                                                            }, this)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                        lineNumber: 1522,
                                                                                        columnNumber: 33
                                                                                    }, this),
                                                                                    profileData ? 'Updating...' : 'Creating...'
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                                                lineNumber: 1521,
                                                                                columnNumber: 31
                                                                            }, this) : profileData ? 'Update Profile' : 'Save Profile'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                                            lineNumber: 1511,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                                    lineNumber: 1502,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/student/profile/page.tsx",
                                                            lineNumber: 1276,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                                    lineNumber: 1264,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/student/profile/page.tsx",
                                                lineNumber: 1263,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/student/profile/page.tsx",
                                        lineNumber: 577,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/student/profile/page.tsx",
                                    lineNumber: 576,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/student/profile/page.tsx",
                                lineNumber: 557,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/student/profile/page.tsx",
                        lineNumber: 496,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/student/profile/page.tsx",
                    lineNumber: 495,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/student/profile/page.tsx",
                lineNumber: 494,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const StudentProfilePage = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Suspense, {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "animate-spin h-10 w-10 text-black",
                xmlns: "http://www.w3.org/2000/svg",
                fill: "none",
                viewBox: "0 0 24 24",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                        className: "opacity-25",
                        cx: "12",
                        cy: "12",
                        r: "10",
                        stroke: "currentColor",
                        strokeWidth: "4"
                    }, void 0, false, {
                        fileName: "[project]/src/app/student/profile/page.tsx",
                        lineNumber: 1577,
                        columnNumber: 13
                    }, void 0),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        className: "opacity-75",
                        fill: "currentColor",
                        d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    }, void 0, false, {
                        fileName: "[project]/src/app/student/profile/page.tsx",
                        lineNumber: 1578,
                        columnNumber: 13
                    }, void 0)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/student/profile/page.tsx",
                lineNumber: 1571,
                columnNumber: 11
            }, void 0)
        }, void 0, false, {
            fileName: "[project]/src/app/student/profile/page.tsx",
            lineNumber: 1570,
            columnNumber: 9
        }, void 0),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(StudentProfileContent, {}, void 0, false, {
            fileName: "[project]/src/app/student/profile/page.tsx",
            lineNumber: 1587,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/student/profile/page.tsx",
        lineNumber: 1568,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = StudentProfilePage;
}}),
"[project]/src/app/student/profile/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/student/profile/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_9460bf73._.js.map