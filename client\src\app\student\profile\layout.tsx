"use client";
import { Separator } from "@/components/ui/separator";
import { SidebarNav } from "./components/sidebar-nav";
import { Progress } from "@/components/ui/progress";
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from "@/store";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { fetchStudentProfile } from '@/store/thunks/studentProfileThunks';
import { AppDispatch } from '@/store';

const sidebarNavItems = [
  {
    title: "Personal Info",
    href: "/student/profile",
  },
  {
    title: "Educational Info",
    href: "/student/profile/educational-info",
  },
  {
    title: "Documents & Photo",
    href: "/student/profile/documents-photo",
  },
];

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default function StudentProfileLayout({ children }: SettingsLayoutProps) {
  const dispatch = useDispatch<AppDispatch>();
  const { profileData, loading: profileLoading } = useSelector(
    (state: RootState) => state.studentProfile
  );
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const studentToken = localStorage.getItem('studentToken');
    if (studentToken) {
      dispatch(fetchStudentProfile());
    }
  }, [dispatch]);

  useEffect(() => {
    if (profileData?.profile) {
      const profile = profileData.profile;
      let completedSections = 0;
      const totalSections = 3;

      // Check Personal Info completion
      if (profile.student?.firstName && profile.student?.lastName && profile.student?.contact && 
          profile.birthday && profile.school && profile.address) {
        completedSections++;
      }

      // Check Educational Info completion
      if (profile.medium && profile.classroom) {
        completedSections++;
      }

      // Check Documents & Photo completion
      if (profile.photo && profile.documentUrl) {
        completedSections++;
      }

      setProgress((completedSections / totalSections) * 100);
    }
  }, [profileData]);

  return (
    <>
      <Header/>
      <div className="space-y-6 p-10 pb-4 md:block">
        <div className="space-y-0.5">
          <h2 className="text-2xl font-bold tracking-tight">
            Student Profile
          </h2>
          <p className="text-muted-foreground">
            Complete your profile information. Your progress will be
            automatically saved as you complete each section.
          </p>
        </div>
        <Progress value={progress} className="h-2" />
        <p className="text-sm text-muted-foreground">
          {Math.round(progress)}% complete
        </p>

        <Separator className="my-6" />
        <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
          <aside className="-mx-4 lg:w-1/6 pb-12">
            <SidebarNav items={sidebarNavItems} />
          </aside>
          <div className="flex justify-center w-full">
            <div className="flex-1 lg:max-w-2xl pb-12">{children}</div>
          </div>
        </div>
      </div>
      <Footer/>
    </>
  );
}
