'use client';

import React, { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const educationalInfoSchema = z.object({
  medium: z.string().min(1, 'Medium of instruction is required'),
  classroom: z.string().min(1, 'Standard is required'),
});

type EducationalInfoFormValues = z.infer<typeof educationalInfoSchema>;

export function EducationalInfoForm() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const { profileData, loading: profileLoading } = useSelector(
    (state: RootState) => state.studentProfile
  );

  const classroomOptions = profileData?.classroomOptions || [];

  const form = useForm<EducationalInfoFormValues>({
    resolver: zodResolver(educationalInfoSchema),
    defaultValues: {
      medium: '',
      classroom: '',
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (!profileData) return;

    const profileObj = profileData.profile;
    
    const formValues = {
      medium: profileObj?.medium || '',
      classroom: profileObj?.classroom || '',
    };

    const currentValues = form.getValues();
    const isFormEmpty = !currentValues.medium && !currentValues.classroom;

    if (isFormEmpty) {
      form.reset(formValues);
    }
  }, [profileData, form]);

  const onSubmit = async (data: EducationalInfoFormValues) => {
    setIsSubmitting(true);

    try {
      const jsonData: any = {
        medium: data.medium,
        classroom: data.classroom,
      };

      const result = await dispatch(updateStudentProfile(jsonData));

      if (result.meta.requestStatus === 'fulfilled') {
        toast.success('Educational information saved successfully!');
        await dispatch(fetchStudentProfile());
        router.push('/student/profile/documents-photo');
      } else {
        toast.error('Failed to save educational information');
      }
    } catch {
      toast.error('Failed to save educational information');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (profileLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <svg
          className="animate-spin h-10 w-10 text-black mb-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        <p className="text-gray-600">Loading profile information...</p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="medium"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Medium *</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || undefined}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Medium" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="english">English</SelectItem>
                    <SelectItem value="gujarati">Gujarati</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="classroom"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Standard *</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || undefined}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Standard" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {classroomOptions.map((option) => (
                      <SelectItem key={option.id} value={option.value}>
                        {option.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-between">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => router.push('/student/profile')}
          >
            Previous
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save & Continue'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
