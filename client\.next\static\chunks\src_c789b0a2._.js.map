{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div data-slot=\"form-item\" className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YAAI,aAAU;YAAY,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAa,GAAG,KAAK;;;;;;;;;;;AAGlF;IARS;MAAA;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAyD;;IAC1F,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAZS;;QACuB;;;MADvB;AAcT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAZS;;QACyD;;;MADzD;AAcT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAFS;AAIT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAbS;AAeT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAbS", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,6LAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;MAFS;AAIT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAFS;AAIT,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAFS;AAIT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAFS", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport {\r\n  format,\r\n  startOfMonth,\r\n  endOfMonth,\r\n  startOfWeek,\r\n  endOfWeek,\r\n  addDays,\r\n  addMonths,\r\n  subMonths,\r\n  isSameMonth,\r\n  isSameDay,\r\n  isToday\r\n} from 'date-fns';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface CalendarProps {\r\n  className?: string;\r\n  selected?: Date;\r\n  onSelect?: (date: Date) => void;\r\n  disabled?: (date: Date) => boolean;\r\n  mode?: 'single' | 'range';\r\n  month?: Date;\r\n  onMonthChange?: (date: Date) => void;\r\n  fromYear?: number;\r\n  toYear?: number;\r\n  captionLayout?: 'buttons' | 'dropdown';\r\n  initialFocus?: boolean;\r\n  classNames?: Record<string, string>;\r\n}\r\n\r\nfunction Calendar({\r\n  className,\r\n  selected,\r\n  onSelect,\r\n  disabled,\r\n  month,\r\n  onMonthChange,\r\n  fromYear,\r\n  toYear,\r\n  captionLayout = 'buttons',\r\n  classNames,\r\n  ...props\r\n}: CalendarProps) {\r\n  const [currentMonth, setCurrentMonth] = React.useState(month || selected || new Date());\r\n\r\n  React.useEffect(() => {\r\n    if (month) {\r\n      setCurrentMonth(month);\r\n    }\r\n  }, [month]);\r\n\r\n  const monthStart = startOfMonth(currentMonth);\r\n  const monthEnd = endOfMonth(monthStart);\r\n  const startDate = startOfWeek(monthStart);\r\n  const endDate = endOfWeek(monthEnd);\r\n\r\n  const dateFormat = 'MMMM yyyy';\r\n  const rows = [];\r\n  let days = [];\r\n  let day = startDate;\r\n  let formattedDate = '';\r\n\r\n  // Generate calendar days\r\n  while (day <= endDate) {\r\n    for (let i = 0; i < 7; i++) {\r\n      formattedDate = format(day, 'd');\r\n      const cloneDay = day;\r\n\r\n      days.push(\r\n        <div\r\n          key={day.toString()}\r\n          className={cn(\r\n            'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer',\r\n            'h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground',\r\n            {\r\n              'text-muted-foreground': !isSameMonth(day, monthStart),\r\n              'bg-primary text-primary-foreground': selected && isSameDay(day, selected),\r\n              'bg-accent text-accent-foreground': isToday(day) && (!selected || !isSameDay(day, selected)),\r\n              'opacity-50 cursor-not-allowed': disabled && disabled(day),\r\n            }\r\n          )}\r\n          onClick={() => {\r\n            if (!disabled || !disabled(cloneDay)) {\r\n              onSelect?.(cloneDay);\r\n            }\r\n          }}\r\n        >\r\n          <span className=\"font-normal\">{formattedDate}</span>\r\n        </div>\r\n      );\r\n      day = addDays(day, 1);\r\n    }\r\n    rows.push(\r\n      <div className=\"flex w-full mt-2\" key={day.toString()}>\r\n        {days}\r\n      </div>\r\n    );\r\n    days = [];\r\n  }\r\n\r\n  const nextMonth = () => {\r\n    const newMonth = addMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const prevMonth = () => {\r\n    const newMonth = subMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(currentMonth.getFullYear(), parseInt(e.target.value), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(parseInt(e.target.value), currentMonth.getMonth(), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-3', className)} {...props}>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Header */}\r\n        <div className={cn('flex justify-center pt-1 relative items-center w-full', classNames?.caption)}>\r\n          {captionLayout === 'dropdown' ? (\r\n            <div className=\"flex gap-2\">\r\n              <select\r\n                value={currentMonth.getMonth()}\r\n                onChange={handleMonthChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: 12 }, (_, i) => (\r\n                  <option key={i} value={i}>\r\n                    {format(new Date(2000, i, 1), 'MMMM')}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <select\r\n                value={currentMonth.getFullYear()}\r\n                onChange={handleYearChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: (toYear || new Date().getFullYear()) - (fromYear || 1950) + 1 }, (_, i) => {\r\n                  const year = (fromYear || 1950) + i;\r\n                  return (\r\n                    <option key={year} value={year}>\r\n                      {year}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={prevMonth}\r\n              >\r\n                <ChevronLeft className=\"size-4\" />\r\n              </Button>\r\n              <div className={cn('text-sm font-medium', classNames?.caption_label)}>\r\n                {format(currentMonth, dateFormat)}\r\n              </div>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={nextMonth}\r\n              >\r\n                <ChevronRight className=\"size-4\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        {/* Calendar Grid */}\r\n        <div className=\"w-full border-collapse space-x-1\">\r\n          {/* Days of week header */}\r\n          <div className=\"flex\">\r\n            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (\r\n              <div\r\n                key={day}\r\n                className=\"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center\"\r\n              >\r\n                {day}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Calendar rows */}\r\n          {rows}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;;;AAnBA;;;;;;AAoCA,SAAS,SAAS,EAChB,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,aAAa,EACb,QAAQ,EACR,MAAM,EACN,gBAAgB,SAAS,EACzB,UAAU,EACV,GAAG,OACW;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,SAAS,YAAY,IAAI;IAEhF,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,IAAI,OAAO;gBACT,gBAAgB;YAClB;QACF;6BAAG;QAAC;KAAM;IAEV,MAAM,aAAa,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE;IAE1B,MAAM,aAAa;IACnB,MAAM,OAAO,EAAE;IACf,IAAI,OAAO,EAAE;IACb,IAAI,MAAM;IACV,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,MAAO,OAAO,QAAS;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,gBAAgB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;YAC5B,MAAM,WAAW;YAEjB,KAAK,IAAI,eACP,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,oGACA;oBACE,yBAAyB,CAAC,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE,KAAK;oBAC3C,sCAAsC,YAAY,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,KAAK;oBACjE,oCAAoC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;oBAC3F,iCAAiC,YAAY,SAAS;gBACxD;gBAEF,SAAS;oBACP,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW;wBACpC,WAAW;oBACb;gBACF;0BAEA,cAAA,6LAAC;oBAAK,WAAU;8BAAe;;;;;;eAjB1B,IAAI,QAAQ;;;;;YAoBrB,MAAM,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,KAAK;QACrB;QACA,KAAK,IAAI,eACP,6LAAC;YAAI,WAAU;sBACZ;WADoC,IAAI,QAAQ;;;;;QAIrD,OAAO,EAAE;IACX;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,IAAI,KAAK,aAAa,WAAW,IAAI,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG;QAChF,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,IAAI,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,aAAa,QAAQ,IAAI;QAC7E,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QAAa,GAAG,KAAK;kBAC7C,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD,YAAY;8BACrF,kBAAkB,2BACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO,aAAa,QAAQ;gCAC5B,UAAU;gCACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,CAAC,GAAG,kBAC9B,6LAAC;wCAAe,OAAO;kDACpB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,GAAG,IAAI;uCADnB;;;;;;;;;;0CAKjB,6LAAC;gCACC,OAAO,aAAa,WAAW;gCAC/B,UAAU;gCACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ,CAAC,UAAU,IAAI,OAAO,WAAW,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI;gCAAE,GAAG,CAAC,GAAG;oCACzF,MAAM,OAAO,CAAC,YAAY,IAAI,IAAI;oCAClC,qBACE,6LAAC;wCAAkB,OAAO;kDACvB;uCADU;;;;;gCAIjB;;;;;;;;;;;6CAIJ;;0CACE,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,YAAY;0CACnD,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;0CAExB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAOhC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;6BAAK,CAAC,GAAG,CAAC,CAAC,oBAC/C,6LAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;wBASV;;;;;;;;;;;;;;;;;;AAKX;GA5KS;KAAA", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAIO,MAAM,iBAAiB;;IAAM,OAAA,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;AAAe;GAAhD;;QAAuB,4JAAA,CAAA,cAAW;;;AACxC,MAAM,iBAAkD,4JAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;;;AANA;;;;;AASA,MAAM,6BAA6B;;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAqB,MAAM,cAAc;;IAE9E,gCAAgC;IAChC,MAAM,aAAa,aAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,IAAI,CAAC,cAAc,YAAY;QAC7B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAE,WAAU;0CAAuD;;;;;;;;;;;;kCAItE,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;wBACV,MAAK;;4BACN;0CACa,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GAtCM;;QACW,qIAAA,CAAA,YAAS;QACA,4JAAA,CAAA,cAAW;;;KAF/B;uCAwCS", "debugId": null}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,8KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,8KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAoCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { io } from 'socket.io-client';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n  userId?: string;\r\n}\r\n\r\nexport default function NotificationBell({ userType, userId }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    // Set up Socket.io connection for real-time notifications\r\n    if (userId) {\r\n      const socketConnection = io(\"https://www.uest.in\", {\r\n        withCredentials: true,\r\n        path: '/uapi/socket.io',\r\n      });\r\n\r\n      socketConnection.on('connect', () => {\r\n        // Join notification room\r\n        socketConnection.emit('join', { username: userId, userType, userId });\r\n      });\r\n\r\n      // Listen for new notifications\r\n      socketConnection.on('newNotification', (notification: Notification) => {\r\n        setNotifications(prev => [notification, ...prev]);\r\n        toast.info(notification.title);\r\n      });\r\n\r\n      // Listen for notification updates (for chat message count updates)\r\n      socketConnection.on('notificationUpdated', (updatedNotification: Notification) => {\r\n        setNotifications(prev =>\r\n          prev.map(notif =>\r\n            notif.id === updatedNotification.id ? updatedNotification : notif\r\n          )\r\n        );\r\n        toast.info(updatedNotification.title);\r\n      });\r\n\r\n      // Listen for notification count updates\r\n      socketConnection.on('notificationCountUpdate', (data: { count: number }) => {\r\n        setUnreadCount(data.count);\r\n      });\r\n\r\n\r\n\r\n      return () => {\r\n        socketConnection.disconnect();\r\n      };\r\n    } else {\r\n      // Fallback to polling if no userId\r\n      const interval = setInterval(fetchNotifications, 30000);\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [userType, userId, fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"relative group rounded-full border-2 border-orange-500 hover:border-orange-400 bg-black hover:bg-gray-900 transition-all duration-200 h-8 w-8 md:h-10 md:w-10\"\r\n        >\r\n          <div className=\"absolute rounded-full inset-0 bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\r\n          <div className=\"relative z-10 flex items-center justify-center\">\r\n            <Bell className=\"h-4 w-4 md:h-5 md:w-5 text-orange-500 group-hover:text-orange-400 transition-colors duration-200\" />\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1 -right-1 md:-top-2 md:-right-2 h-4 w-4 md:h-5 md:w-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-bold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n        <div className=\"p-4 border-b\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h3 className=\"font-semibold\">Notifications</h3>\r\n            <div className=\"flex gap-2\">\r\n              {unreadCount > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleMarkAllAsRead}\r\n                  className=\"text-xs\"\r\n                >\r\n                  Mark all read\r\n                </Button>\r\n              )}\r\n              {notifications.length > 0 && unreadCount === 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleRemoveAllClick}\r\n                  className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  Remove all\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"h-80 overflow-y-auto\">\r\n          {loading ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              Loading notifications...\r\n            </div>\r\n          ) : notifications.length === 0 ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              No notifications yet\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y\">\r\n              {Array.isArray(notifications) && notifications.map((notification) => (\r\n                <div\r\n                  key={notification.id}\r\n                  className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${\r\n                    !notification.isRead ? 'bg-blue-50/50' : ''\r\n                  }`}\r\n                  onClick={() => handleNotificationClick(notification)}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className={`w-2 h-2 rounded-full mt-2 ${\r\n                      !notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                    }`} />\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                      <p className=\"text-sm text-muted-foreground mt-1\">\r\n                        {notification.message}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground mt-2\">\r\n                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {safeNotifications.length > 0 && (\r\n          <div className=\"p-3 border-t bg-muted/30\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs\"\r\n              onClick={() => {\r\n                setIsOpen(false);\r\n                router.push('/notifications');\r\n              }}\r\n            >\r\n              View All Notifications\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n\r\n    {/* Delete Confirmation Dialog */}\r\n    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            Are you sure you want to remove all notifications? This action cannot be undone.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction\r\n            onClick={handleConfirmRemoveAll}\r\n            className=\"bg-red-600 hover:bg-red-700\"\r\n          >\r\n            Remove All\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;AAAA;AACA;;;AArCA;;;;;;;;;;;AA4Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAE,MAAM,EAAyB;;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI;gBACF,WAAW;gBACX,IAAI;gBACJ,IAAI;gBAEJ,IAAI,aAAa,SAAS;oBACxB,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;oBACxC,QAAQ,MAAM,CAAA,GAAA,yIAAA,CAAA,sBAAmB,AAAD;gBAClC,OAAO;oBACL,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;oBAC1C,QAAQ,MAAM,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD;gBACpC;gBAEA,2CAA2C;gBAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;gBACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;gBACpD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,iBAAiB,EAAE;gBACnB,eAAe;YACjB,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,yIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,yIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,yIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,yIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;YAEA,0DAA0D;YAC1D,IAAI,QAAQ;gBACV,MAAM,mBAAmB,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;oBACjD,iBAAiB;oBACjB,MAAM;gBACR;gBAEA,iBAAiB,EAAE,CAAC;kDAAW;wBAC7B,yBAAyB;wBACzB,iBAAiB,IAAI,CAAC,QAAQ;4BAAE,UAAU;4BAAQ;4BAAU;wBAAO;oBACrE;;gBAEA,+BAA+B;gBAC/B,iBAAiB,EAAE,CAAC;kDAAmB,CAAC;wBACtC;0DAAiB,CAAA,OAAQ;oCAAC;uCAAiB;iCAAK;;wBAChD,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,aAAa,KAAK;oBAC/B;;gBAEA,mEAAmE;gBACnE,iBAAiB,EAAE,CAAC;kDAAuB,CAAC;wBAC1C;0DAAiB,CAAA,OACf,KAAK,GAAG;kEAAC,CAAA,QACP,MAAM,EAAE,KAAK,oBAAoB,EAAE,GAAG,sBAAsB;;;wBAGhE,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,oBAAoB,KAAK;oBACtC;;gBAEA,wCAAwC;gBACxC,iBAAiB,EAAE,CAAC;kDAA2B,CAAC;wBAC9C,eAAe,KAAK,KAAK;oBAC3B;;gBAIA;kDAAO;wBACL,iBAAiB,UAAU;oBAC7B;;YACF,OAAO;gBACL,mCAAmC;gBACnC,MAAM,WAAW,YAAY,oBAAoB;gBACjD;kDAAO,IAAM,cAAc;;YAC7B;QACF;qCAAG;QAAC;QAAU;QAAQ;KAAmB;IAEzC,qBACE;;0BACE,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACrC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOxC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,6LAAC;gCAAI,WAAU;0CACZ,wBACC,6LAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,6LAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,6LAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,6LAAC;4CAEC,WAAW,CAAC,uDAAuD,EACjE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IACzC;4CACF,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,0BAA0B,EACzC,CAAC,aAAa,MAAM,GAAG,gBAAgB,eACvC;;;;;;kEACF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,6LAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAhB1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAyB7B,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQX;GA5RwB;;QAOP,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;KARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  localStorage.removeItem('studentToken');\r\n  localStorage.removeItem('student_data');\r\n  return {\r\n    success: true,\r\n    message: 'Logged out successfully',\r\n  };\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 2292, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;uDAAc;oBAClB,IAAI,CAAC,WAAW;wBACd,UAAU;wBACV;oBACF;oBACA,MAAM,WAAmC,MAAM,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EAAE;oBACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;oBACpC,OAAO;wBACL,UAAU;oBACZ;gBACF;;YACA;QACF;kCAAG;QAAC;KAAU;IAEd,qBACK,6LAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;GAxBM;KAAA;uCA0BS", "debugId": null}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, ChevronRight, LayoutDashboard, BadgeCent, MessageSquare, GraduationCap, Flame } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n} from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { motion, useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\n\r\n\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n \r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem('student_data');\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem('student_data');\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((time, delta) => {\r\n    if (isHovering || contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event('storage'));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem('student_data');\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\", icon: <Flame className=\"w-4 h-4\" />, isNew: true },\r\n    {\r\n      href: \"/mock-exam-card\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <span>Daily Quiz</span>\r\n          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}\r\n        </span>\r\n      ),\r\n      icon: <BadgeCent className=\"w-4 h-4\" />,\r\n    },\r\n   \r\n    { href: \"/careers\", label: \"Career\", icon: <Briefcase className=\"w-4 h-4\" /> },\r\n  ];\r\n\r\n  const bannerContent = (\r\n    <div className=\"inline-flex items-center space-x-4 whitespace-nowrap\">\r\n      <span className=\"text-sm md:text-xl font-semibold text-black\">\r\n        U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion\r\n      </span>\r\n      <button\r\n        className=\"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition\"\r\n        style={{ border: '2px solid black' }}\r\n        onClick={() => router.push(`/uwhiz-info/${1}`)}\r\n      >\r\n        Apply Now <ChevronRight className=\"ml-2 h-4 w-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black overflow-x-hidden\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-20 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={150}\r\n                height={50}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-4\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.icon}\r\n                  {link.label}\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* Mobile Notification Bell */}\r\n            <div className=\"flex md:hidden items-center space-x-2\">\r\n              {isAuthenticated && (\r\n                <NotificationBell userType=\"class\" userId={user?.id} />\r\n              )}\r\n              {isStudentLoggedIn && (\r\n                <NotificationBell userType=\"student\" userId={studentData?.id} />\r\n              )}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n              </Button>\r\n            </div>\r\n\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <NotificationBell userType=\"class\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full border-2 border-orange-500 group bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              <div className=\"h-8 border-l border-orange-500/20\" />\r\n\r\n              {isAuthenticated && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                            : \"CT\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName} ${user.lastName}`\r\n                            : user?.className || \"Class Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{user?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/profile\" className=\"flex items-center\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button onClick={() => accessClassDashboard()} className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <User className=\"mr-2 h-4 w-4\" />\r\n                        <span>My Dashboard</span>\r\n                      </Button>\r\n                      {/* <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/question-bank\" className=\"flex items-center\">\r\n                          <FileQuestion className=\"mr-2 h-4 w-4\" />\r\n                          <span>Question Bank</span>\r\n                        </Link>\r\n                      </Button> */}\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                            if (response.data.success) {\r\n                              router.push(\"/\");\r\n                              dispatch(clearUser());\r\n                              localStorage.removeItem(\"token\");\r\n                              toast.success(\"Logged out successfully\");\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Logout error:\", error);\r\n                            toast.error(\"Failed to logout\");\r\n                          }\r\n                        }}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  className=\"bg-customOrange hover:bg-[#E88143] text-white mr-4\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/class/login\">Join as a Tutor/Class</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/student/login\">Student Login</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  <NotificationBell userType=\"student\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full group border-2 border-orange-500 bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName} ${studentData.lastName}`\r\n                            : \"Student Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{studentData?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/profile\" className=\"flex items-center\">\r\n                          <UserCircle className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/wishlist\" className=\"flex items-center\">\r\n                          <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Wishlist</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={handleStudentLogout}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"w-screen bg-[#FD904B] border-y border-black relative mt-1\">\r\n          <div className=\"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0\"></div>\r\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden\">\r\n            <motion.div\r\n              className=\"inline-flex py-2 px-4\"\r\n              style={{ x }}\r\n              onMouseEnter={() => setIsHovering(true)}\r\n              onMouseLeave={() => setIsHovering(false)}\r\n            >\r\n              <div ref={contentRef} className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                {bannerContent}\r\n              </div>\r\n              <div className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                {bannerContent}\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${\r\n            isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex justify-end\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                <X className=\"h-6 w-6\" />\r\n              </Button>\r\n            </div>\r\n\r\n            <nav className=\"flex flex-col space-y-2 mt-8\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {link.icon}\r\n                    {typeof link.label === \"string\" ? (\r\n                      <span>{link.label}</span>\r\n                    ) : (\r\n                      link.label\r\n                    )}\r\n                  </div>\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      New\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Link href=\"/classes/profile\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <User className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Profile</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                    onClick={() => accessClassDashboard()}\r\n                  >\r\n                    <div className=\"absolute inset-0\" />\r\n                    <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                      <div className=\"p-1.5 rounded-full\">\r\n                        <LayoutDashboard className=\"h-5 w-5 text-white\" />\r\n                      </div>\r\n                      <span className=\"font-medium text-gray-300\">My Dashboard</span>\r\n                    </div>\r\n                  </Button>\r\n                  {/* <Link href=\"/classes/question-bank\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <FileQuestion className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Question Bank</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link> */}\r\n                  <Link href=\"/classes/referral-dashboard\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Share2 className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Referral Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 hover:text-white mt-3\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {studentData?.firstName && studentData?.lastName && (\r\n                    <div className=\"p-3 border border-[#ff914d]/20 rounded-lg bg-white\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">{`${studentData.firstName} ${studentData.lastName}`}</p>\r\n                          <p className=\"text-xs text-gray-600\">{studentData.email}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/profile\" className=\"flex items-center justify-center gap-3\">\r\n                      <UserCircle className=\"h-5 w-5\" />\r\n                      <span>Profile</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/wishlist\" className=\"flex items-center justify-center gap-3\">\r\n                      <ShoppingBag className=\"h-5 w-5\" />\r\n                      <span>My Wishlist</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/referral-dashboard\" className=\"flex items-center justify-center gap-3\">\r\n                      <Share2 className=\"h-5 w-5\" />\r\n                      <span>Referral Dashboard</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-3 pt-3\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor/Classes Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-customOrange text-orange-500 hover:bg-orange\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;AAyI+B;;AAvI/B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AA/BA;;;;;;;;;;;;;;;;;;;;;;;AAmCA,MAAM,SAAS;;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;8BAAE,CAAC,QAAqB,MAAM,IAAI;;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAG7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YACxC,qBAAqB;YAErB,IAAI,YAAY;gBACd,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;YAC7B;YAEA,MAAM;wDAAsB;oBAC1B,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;oBAC5C,qBAAqB;oBACrB,IAAI,gBAAgB;wBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;wBACxC,IAAI,YAAY;4BACd,eAAe,KAAK,KAAK,CAAC;wBAC5B;wBACA,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;oBAC7B,OAAO;wBACL,eAAe;oBACjB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC,IAAI,WAAW,OAAO,EAAE;gBACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;gBAC9D,gBAAgB;YAClB;YAEA;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;2BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD;oCAAE,CAAC,MAAM;YACvB,IAAI,cAAc,iBAAiB,GAAG;YACtC,MAAM,WAAW,EAAE,GAAG;YACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;YACjC,IAAI,OAAO,WAAW;YACtB,IAAI,QAAQ,CAAC,cAAc;gBACzB,OAAO;YACT;YACA,EAAE,GAAG,CAAC;QACR;;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;gBAC/B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,sHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;YAAc,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAAa;QAC9F;YAAE,MAAM;YAAU,OAAO;YAAY,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAK;QACtF;YACE,MAAM;YACN,qBACE,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;kCAAK;;;;;;oBACL,mCAAqB,6LAAC,iJAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;YAGjE,oBAAM,6LAAC,mNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QAEA;YAAE,MAAM;YAAY,OAAO;YAAU,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;KAC9E;IAED,MAAM,8BACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAA8C;;;;;;0BAG9D,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAkB;gBACnC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,GAAG;;oBAC9C;kCACW,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;IAKxC,qBACE;;0BACE,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,KAAK,IAAI;gDACT,KAAK,KAAK;gDACV,KAAK,KAAK,KAAK,+BACd,6LAAC;oDAAK,WAAU;8DAAiE;;;;;;gDAIlF,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DAA+E;;;;;;;2CAZ5F,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;wCACZ,iCACC,6LAAC,gJAAA,CAAA,UAAgB;4CAAC,UAAS;4CAAQ,QAAQ,MAAM;;;;;;wCAElD,mCACC,6LAAC,gJAAA,CAAA,UAAgB;4CAAC,UAAS;4CAAU,QAAQ,aAAa;;;;;;sDAE5D,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;sDAER,2BAAa,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAAe,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI9D,6LAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,6LAAC,gJAAA,CAAA,UAAgB;oDAAC,UAAS;;;;;;8DAC3B;8DACA,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,QAAQ;kEACxB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAI;wEACJ,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAMjC,6LAAC;4CAAI,WAAU;;;;;;wCAEd,iCACC,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;;;;;;8DAIV,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;8EAGR,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa;;;;;;sFAEzB,6LAAC;4EAAE,WAAU;sFAAyB,MAAM,aAAa;;;;;;;;;;;;;;;;;;sEAI7D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAS,IAAM;oEAAwB,WAAU;;sFACvD,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;sFAAK;;;;;;;;;;;;8EAQR,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAIV,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;wEACP,IAAI;4EACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4EAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gFACzB,OAAO,IAAI,CAAC;gFACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD;gFACjB,aAAa,UAAU,CAAC;gFACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4EAChB;wEACF,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,iBAAiB;4EAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wEACd;oEACF;8EACD;;;;;;;;;;;;;;;;;;;;;;;;wCAQR,CAAC,mBAAmB,CAAC,mCACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;wCAI7B,CAAC,mBAAmB,CAAC,mCACpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;wCAI/B,mCACC;;8DACE,6LAAC,gJAAA,CAAA,UAAgB;oDAAC,UAAS;;;;;;8DAC3B;8DACA,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,QAAQ;kEACxB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAI;wEACJ,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;;;wCAMhC,mCACC,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;8DAIV,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;8EAGR,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFACV,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;sFAEN,6LAAC;4EAAE,WAAU;sFAAyB,aAAa,aAAa;;;;;;;;;;;;;;;;;;sEAIpE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAoB,WAAU;;0FACvC,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;8EACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE;oCAAE;oCACX,cAAc,IAAM,cAAc;oCAClC,cAAc,IAAM,cAAc;;sDAElC,6LAAC;4CAAI,KAAK;4CAAY,WAAU;sDAC7B;;;;;;sDAEH,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,6LAAC;;kCACC,6LAAC;wBACC,WAAW,CAAC,mJAAmJ,EAC7J,aAAa,kBAAkB,oBAC/B;kCAEF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;wDACT,OAAO,KAAK,KAAK,KAAK,yBACrB,6LAAC;sEAAM,KAAK,KAAK;;;;;mEAEjB,KAAK,KAAK;;;;;;;gDAGb,KAAK,KAAK,KAAK,+BACd,6LAAC;oDAAK,WAAU;8DAA+E;;;;;;gDAIhG,KAAK,KAAK,kBACT,6LAAC;oDAAK,WAAU;8DAAiE;;;;;;;2CAnB9E,KAAK,IAAI;;;;;;;;;;8CA2BpB,6LAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,QAAQ;8DACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM;;sEAEf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+NAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;;;;;;8EAE7B,6LAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;8DAkBhD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,QAAQ;8DAC/C,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,sHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4DAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;8DAEA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,mCACC;;gDACG,aAAa,aAAa,aAAa,0BACtC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,AAAC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAE,WAAW;;;;;;;;;;;0EAG1E,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA0B,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;;;;;;kFACzF,6LAAC;wEAAE,WAAU;kFAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAK/D,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;;0EACtC,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAoB,WAAU;;0EACvC,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;8DAEA,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,CAAC,mBAAmB,CAAC,mCACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,6LAAC,0JAAA,CAAA,UAA0B;;;;;;;;;;;;;AAIzD;GA3vBM;;QAC8B,4JAAA,CAAA,cAAW;QAI5B,wHAAA,CAAA,iBAAc;QAChB,qIAAA,CAAA,YAAS;QAKd,qLAAA,CAAA,iBAAc;QA0CxB,wLAAA,CAAA,oBAAiB;;;KArDb;uCA6vBS", "debugId": null}}, {"offset": {"line": 4265, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/separator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = 'horizontal',\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Separator };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 4301, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/progress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KAlBS", "debugId": null}}, {"offset": {"line": 4346, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/careers\" className=\"hover:text-white transition\">\r\n                  Careers\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/student/login\" className=\"hover:text-white transition\">\r\n                  Student Login\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/uwhiz\" className=\"hover:text-white transition\">\r\n                  Uwhiz\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,iJAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,iJAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,iJAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,6LAAC;oCAAgB,WAAU;8CACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,6LAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAA8B;;;;;;;;;;;sDAItE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;KA5IM;uCA8IS", "debugId": null}}, {"offset": {"line": 4763, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/components/sidebar-nav.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\nimport { RootState } from '@/store';\nimport { CheckCircle } from 'lucide-react';\n\ninterface SidebarNavProps {\n  items: { title: string; href: string }[];\n  activeSection: string;\n  setActiveSection: (section: string) => void;\n}\n\nexport function SidebarNav({ items, activeSection, setActiveSection }: SidebarNavProps) {\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\n\n  const isFormCompleted = (title: string) => {\n    if (!profileData?.profile) return false;\n    \n    const profile = profileData.profile;\n    \n    switch (title) {\n      case \"Personal Info\":\n        return !!(profile.student?.firstName && profile.student?.lastName && \n                 profile.student?.contact && profile.birthday && profile.school && profile.address);\n      case \"Educational Info\":\n        return !!(profile.medium && profile.classroom);\n      case \"Documents & Photo\":\n        return !!(profile.photo && profile.documentUrl);\n      default:\n        return false;\n    }\n  };\n\n  return (\n    <nav className=\"space-y-1\">\n      {items.map((item, index) => {\n        const isActive = activeSection === item.href.replace('#', '');\n        const isCompleted = isFormCompleted(item.title);\n        \n        // Disable if previous form is not completed (except for first item)\n        const isDisabled = index > 0 && !isFormCompleted(items[index - 1].title);\n\n        return (\n          <button\n            key={item.href}\n            onClick={() => !isDisabled && setActiveSection(item.href.replace('#', ''))}\n            className={`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${\n              isActive \n                ? 'bg-muted text-primary' \n                : isDisabled \n                ? 'text-gray-400 cursor-not-allowed' \n                : 'text-muted-foreground hover:text-primary'\n            }`}\n            disabled={isDisabled}\n          >\n            <span>{item.title}</span>\n            {isCompleted && <CheckCircle size={16} className=\"text-green-500\" />}\n          </button>\n        );\n      })}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;;AAQO,SAAS,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,gBAAgB,EAAmB;;IACpF,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;kCAAE,CAAC,QAAqB,MAAM,cAAc;;IAE9E,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,aAAa,SAAS,OAAO;QAElC,MAAM,UAAU,YAAY,OAAO;QAEnC,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,OAAO,EAAE,aAAa,QAAQ,OAAO,EAAE,YAChD,QAAQ,OAAO,EAAE,WAAW,QAAQ,QAAQ,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO;YAC5F,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,MAAM,IAAI,QAAQ,SAAS;YAC/C,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,QAAQ,WAAW;YAChD;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,WAAW,kBAAkB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;YAC1D,MAAM,cAAc,gBAAgB,KAAK,KAAK;YAE9C,oEAAoE;YACpE,MAAM,aAAa,QAAQ,KAAK,CAAC,gBAAgB,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK;YAEvE,qBACE,6LAAC;gBAEC,SAAS,IAAM,CAAC,cAAc,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;gBACtE,WAAW,CAAC,uGAAuG,EACjH,WACI,0BACA,aACA,qCACA,4CACJ;gBACF,UAAU;;kCAEV,6LAAC;kCAAM,KAAK,KAAK;;;;;;oBAChB,6BAAe,6LAAC,8NAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAU;;;;;;;eAZ5C,KAAK,IAAI;;;;;QAepB;;;;;;AAGN;GAlDgB;;QACU,4JAAA,CAAA,cAAW;;;KADrB", "debugId": null}}, {"offset": {"line": 4849, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect, Suspense } from 'react';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { useForm } from 'react-hook-form';\nimport { z } from 'zod';\nimport { toast } from 'sonner';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { format } from 'date-fns';\nimport { Calendar as CalendarIcon, FileText, X, Camera, Upload, Check } from 'lucide-react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '@/store';\nimport { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';\nimport { updateProfilePhoto } from '@/store/slices/studentProfileSlice';\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { Textarea } from '@/components/ui/textarea';\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Calendar } from '@/components/ui/calendar';\nimport { cn } from '@/lib/utils';\nimport Image from 'next/image';\nimport Header from '../../../app-components/Header';\nimport { Separator } from \"@/components/ui/separator\";\nimport { Progress } from \"@/components/ui/progress\";\nimport Footer from \"@/app-components/Footer\";\nimport { SidebarNav } from \"./components/sidebar-nav\";\n\nconst profileFormSchema = z.object({\n  firstName: z.string().min(2, 'First name must be at least 2 characters.'),\n  middleName: z.string().optional(),\n  lastName: z.string().min(2, 'Last name must be at least 2 characters.'),\n  mothersName: z.string().optional(),\n  email: z.string().email('Please enter a valid email address.').optional().or(z.literal('')),\n  contact: z\n    .string()\n    .min(10, 'Contact number must be at least 10 digits.')\n    .max(15, 'Contact number must not exceed 15 digits.')\n    .regex(/^\\d+$/, 'Contact number must contain only digits.'),\n  contact2: z\n    .string()\n    .min(10, 'Contact number must be at least 10 digits.')\n    .max(15, 'Contact number must not exceed 15 digits.')\n    .regex(/^\\d+$/, 'Contact number must contain only digits.')\n    .optional()\n    .or(z.literal('')),\n  medium: z.string().min(1, 'Medium of instruction is required'),\n  classroom: z.string().min(1, 'Standard is required'),\n  gender: z.string().optional(),\n  birthday: z.date({ required_error: 'Please select your date of birth' }),\n  school: z.string().min(2, 'School name must be at least 2 characters.'),\n  address: z.string().min(5, 'Address must be at least 5 characters.'),\n  age: z.string().optional(),\n  aadhaarNumber: z\n    .string()\n    .min(12, 'Aadhaar number must be 12 digits.')\n    .max(12, 'Aadhaar number must be 12 digits.')\n    .regex(/^\\d+$/, 'Aadhaar number must contain only digits.')\n    .optional()\n    .or(z.literal('')),\n  bloodGroup: z.string().optional(),\n  birthPlace: z.string().optional(),\n  motherTongue: z.string().optional(),\n  religion: z.string().optional(),\n  caste: z.string().optional(),\n  subCaste: z.string().optional(),\n  photo: z.any().optional(),\n  document: z.any().optional(),\n});\n\ntype ProfileFormValues = z.infer<typeof profileFormSchema>;\n\nconst sidebarNavItems = [\n  {\n    title: \"Personal Info\",\n    href: \"#personal-info\",\n  },\n  {\n    title: \"Educational Info\",\n    href: \"#educational-info\",\n  },\n  {\n    title: \"Documents & Photo\",\n    href: \"#documents-photo\",\n  },\n];\n\nconst StudentProfileContent = () => {\n  const router = useRouter();\n  const dispatch = useDispatch<AppDispatch>();\n  const searchParams = useSearchParams();\n  const fromQuiz = searchParams.get('quiz') === 'true';\n  const examId = searchParams.get('examId');\n\n  const [activeSection, setActiveSection] = useState('personal-info');\n  const [photo, setPhoto] = useState<string | null>(null);\n  const [isCameraOpen, setIsCameraOpen] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [cameraError, setCameraError] = useState<string | null>(null);\n  const [uploadedDocument, setUploadedDocument] = useState<\n    File | { name: string; size: number; url: string; type: string } | null\n  >(null);\n  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);\n  const [progress, setProgress] = useState(0);\n\n  const { profileData, loading: profileLoading } = useSelector(\n    (state: RootState) => state.studentProfile\n  );\n\n  const profile = profileData?.profile || null;\n  const classroomOptions = profileData?.classroomOptions || [];\n\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  const form = useForm<ProfileFormValues>({\n    resolver: zodResolver(profileFormSchema),\n    defaultValues: {\n      firstName: '',\n      middleName: '',\n      lastName: '',\n      mothersName: '',\n      email: '',\n      contact: '',\n      contact2: '',\n      medium: '',\n      classroom: '',\n      gender: '',\n      birthday: undefined,\n      school: '',\n      address: '',\n      age: '',\n      aadhaarNumber: '',\n      bloodGroup: '',\n      birthPlace: '',\n      motherTongue: '',\n      religion: '',\n      caste: '',\n      subCaste: '',\n    },\n    mode: 'onSubmit',\n  });\n\n  useEffect(() => {\n    const studentToken = localStorage.getItem('studentToken');\n    if (!studentToken) {\n      toast.error('Please login to access your profile');\n      router.push('/');\n    }\n  }, [router]);\n\n  useEffect(() => {\n    const studentToken = localStorage.getItem('studentToken');\n    if (studentToken) {\n      dispatch(fetchStudentProfile());\n    }\n  }, [dispatch]);\n\n  // Calculate progress based on completed sections\n  useEffect(() => {\n    if (profileData?.profile) {\n      const profile = profileData.profile;\n      let completedSections = 0;\n      const totalSections = 3;\n\n      // Check Personal Info completion\n      if (profile.student?.firstName && profile.student?.lastName && profile.student?.contact &&\n          profile.birthday && profile.school && profile.address) {\n        completedSections++;\n      }\n\n      // Check Educational Info completion\n      if (profile.medium && profile.classroom) {\n        completedSections++;\n      }\n\n      // Check Documents & Photo completion\n      if (profile.photo && profile.documentUrl) {\n        completedSections++;\n      }\n\n      setProgress((completedSections / totalSections) * 100);\n    }\n  }, [profileData]);\n\n  // Load existing data into form\n  useEffect(() => {\n    if (!profileData) return;\n\n    const profileObj = profileData.profile;\n    const studentData = profileObj?.student || JSON.parse(localStorage.getItem('student_data') || '{}');\n\n    const formValues = {\n      firstName: studentData?.firstName || '',\n      middleName: studentData?.middleName || '',\n      lastName: studentData?.lastName || '',\n      mothersName: studentData?.mothersName || '',\n      email: studentData?.email || '',\n      contact: studentData?.contact || '',\n      contact2: profileObj?.contactNo2 || '',\n      medium: profileObj?.medium || '',\n      classroom: profileObj?.classroom || '',\n      gender: profileObj?.gender || '',\n      birthday: profileObj?.birthday ? new Date(profileObj.birthday) : undefined,\n      school: profileObj?.school || '',\n      address: profileObj?.address || '',\n      age: profileObj?.age?.toString() || '',\n      aadhaarNumber: profileObj?.aadhaarNo || '',\n      bloodGroup: profileObj?.bloodGroup || '',\n      birthPlace: profileObj?.birthPlace || '',\n      motherTongue: profileObj?.motherTongue || '',\n      religion: profileObj?.religion || '',\n      caste: profileObj?.caste || '',\n      subCaste: profileObj?.subCaste || '',\n    };\n\n    if (profileObj?.photo && !photo) {\n      setPhoto(profileObj.photo);\n      form.setValue('photo', profileObj.photo);\n    }\n\n    if (profileObj?.documentUrl && !uploadedDocument && !isDocumentRemoved) {\n      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';\n      const documentUrl = profileObj.documentUrl.startsWith('http')\n        ? profileObj.documentUrl\n        : `${baseUrl}${profileObj.documentUrl}`;\n\n      const documentObj = {\n        name: documentUrl.split('/').pop() || 'Uploaded Document',\n        size: 0,\n        url: documentUrl,\n        type: 'application/octet-stream',\n      };\n\n      setUploadedDocument(documentObj);\n      form.setValue('document', documentObj);\n    }\n\n    const currentValues = form.getValues();\n    const isFormEmpty = !currentValues.firstName && !currentValues.lastName && !currentValues.contact;\n    const isEducationalDataMissing = !currentValues.medium || !currentValues.classroom;\n\n    if (isFormEmpty || isEducationalDataMissing) {\n      form.reset(formValues);\n    }\n  }, [profileData, form, photo, uploadedDocument, isDocumentRemoved]);\n\n  const openCamera = async () => {\n    setCameraError(null);\n\n    try {\n      if (!navigator.mediaDevices?.getUserMedia) {\n        throw new Error('Camera not supported on this device');\n      }\n\n      setIsCameraOpen(true);\n\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { facingMode: 'user' },\n      });\n\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n        videoRef.current.onloadedmetadata = () => {\n          videoRef.current?.play().catch(() => toast.error('Error starting camera preview'));\n        };\n      }\n    } catch (error: any) {\n      setIsCameraOpen(false);\n      const message = error.name === 'NotAllowedError'\n        ? 'Please allow camera access in your browser settings.'\n        : 'Could not access camera. Please check your camera settings.';\n      setCameraError(message);\n      toast.error(message);\n    }\n  };\n\n  const compressImage = (canvas: HTMLCanvasElement, maxWidth: number = 800, quality: number = 0.6): string => {\n    const context = canvas.getContext('2d');\n    if (!context) return '';\n\n    const originalWidth = canvas.width;\n    const originalHeight = canvas.height;\n\n    let newWidth = originalWidth;\n    let newHeight = originalHeight;\n\n    if (originalWidth > maxWidth) {\n      newWidth = maxWidth;\n      newHeight = (originalHeight * maxWidth) / originalWidth;\n    }\n\n    const compressedCanvas = document.createElement('canvas');\n    compressedCanvas.width = newWidth;\n    compressedCanvas.height = newHeight;\n\n    const compressedContext = compressedCanvas.getContext('2d');\n    if (!compressedContext) return '';\n\n    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);\n\n    return compressedCanvas.toDataURL('image/jpeg', quality);\n  };\n\n  const capturePhoto = () => {\n    if (!videoRef.current || !canvasRef.current) return;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const context = canvas.getContext('2d');\n\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    context?.clearRect(0, 0, canvas.width, canvas.height);\n    context?.save();\n    context?.scale(-1, 1);\n    context?.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);\n    context?.restore();\n\n    const compressedPhotoDataUrl = compressImage(canvas, 800, 0.6);\n    const base64Data = compressedPhotoDataUrl.split(',')[1];\n    const sizeInKB = (base64Data.length * 3) / 4 / 1024;\n\n    if (sizeInKB > 5120) {\n      toast.error('Photo size exceeds 5MB limit. Please try again.');\n      return;\n    }\n\n    setPhoto(compressedPhotoDataUrl);\n    form.setValue('photo', compressedPhotoDataUrl);\n    dispatch(updateProfilePhoto(compressedPhotoDataUrl));\n\n    closeCamera();\n  };\n\n  const closeCamera = () => {\n    if (videoRef.current?.srcObject) {\n      const stream = videoRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      videoRef.current.srcObject = null;\n    }\n    setIsCameraOpen(false);\n    setCameraError(null);\n  };\n\n  const removeDocument = () => {\n    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {\n      URL.revokeObjectURL(uploadedDocument.url);\n    }\n\n    setUploadedDocument(null);\n    setIsDocumentRemoved(true);\n    const fileInput = document.getElementById('document') as HTMLInputElement;\n    if (fileInput) fileInput.value = '';\n    form.setValue('document', null);\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes < 1024) return bytes + ' bytes';\n    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';\n    else return (bytes / 1048576).toFixed(1) + ' MB';\n  };\n\n\n\n  const onSubmit = async (data: ProfileFormValues) => {\n    setIsSubmitting(true);\n\n    try {\n      const currentPhoto = photo || profileData?.profile?.photo;\n      if (!currentPhoto) {\n        toast.error('Please capture a photo for your profile');\n        setIsSubmitting(false);\n        return;\n      }\n\n      if (!uploadedDocument || isDocumentRemoved) {\n        toast.error('Identity document is required. Please upload a document.');\n        setIsSubmitting(false);\n        return;\n      }\n\n      if (!(await form.trigger())) {\n        toast.error('Please fill in all required fields correctly');\n        setIsSubmitting(false);\n        return;\n      }\n\n      const jsonData: any = {\n        firstName: data.firstName,\n        middleName: data.middleName,\n        lastName: data.lastName,\n        mothersName: data.mothersName,\n        email: data.email,\n        contact: data.contact,\n        contact2: data.contact2,\n        medium: data.medium,\n        classroom: data.classroom,\n        gender: data.gender,\n        birthday: data.birthday?.toISOString() || '',\n        school: data.school,\n        address: data.address,\n        age: data.age,\n        aadhaarNumber: data.aadhaarNumber,\n        bloodGroup: data.bloodGroup,\n        birthPlace: data.birthPlace,\n        motherTongue: data.motherTongue,\n        religion: data.religion,\n        caste: data.caste,\n        subCaste: data.subCaste,\n      };\n\n      if (photo?.startsWith('data:')) {\n        const base64Data = photo.split(',')[1];\n        const sizeInKB = (base64Data.length * 3) / 4 / 1024;\n\n        if (sizeInKB > 5120) {\n          toast.error('Photo size exceeds 5MB limit.');\n          return;\n        }\n\n        jsonData.photo = base64Data;\n        jsonData.photoMimeType = 'image/jpeg';\n      }\n\n      if (uploadedDocument instanceof File || (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:'))) {\n        const documentFile = uploadedDocument instanceof File\n          ? uploadedDocument\n          : await fetch(uploadedDocument.url)\n              .then(res => res.blob())\n              .then(blob => new File([blob], uploadedDocument.name, { type: uploadedDocument.type }));\n\n        const documentBase64 = await new Promise<string>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = () => resolve((reader.result as string).split(',')[1]);\n          reader.onerror = reject;\n          reader.readAsDataURL(documentFile);\n        });\n\n        const docSizeKB = (documentBase64.length * 3) / 4 / 1024;\n        if (docSizeKB > 5120) {\n          toast.error('Document size exceeds 5MB limit.');\n          return;\n        }\n\n        jsonData.document = documentBase64;\n        jsonData.documentMimeType = documentFile.type;\n        jsonData.documentName = documentFile.name;\n      }\n\n      // Handle document removal\n      if (isDocumentRemoved && profileData?.profile?.documentUrl) {\n        jsonData.removeDocument = true;\n      }\n\n      const studentToken = localStorage.getItem('studentToken');\n      if (!studentToken) {\n        toast.error('Please login to submit your profile');\n        router.push('/');\n        return;\n      }\n\n      const result = await dispatch(updateStudentProfile(jsonData));\n\n      if (result.meta.requestStatus === 'fulfilled') {\n        toast.success(`Profile ${profile ? 'updated' : 'created'} successfully!`);\n\n        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');\n        const studentData = {\n          ...existingStudentData,\n          id: existingStudentData.id || profileData?.profile?.student?.id || '',\n          firstName: data.firstName,\n          middleName: data.middleName,\n          lastName: data.lastName,\n          mothersName: data.mothersName,\n          email: data.email || existingStudentData.email || profileData?.profile?.student?.email || '',\n          contact: data.contact,\n        };\n\n        localStorage.setItem('student_data', JSON.stringify(studentData));\n        setIsDocumentRemoved(false);\n        await dispatch(fetchStudentProfile());\n\n        if (fromQuiz) {\n          if (examId) {\n            router.push(`/uwhiz-exam/${examId}`);\n          } else {\n            router.push('/mock-test');\n          }\n        } else {\n          router.push('/');\n        }\n      } else if (result.meta.requestStatus === 'rejected') {\n        const errorMessage = result.payload as string;\n\n        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {\n          toast.error('Your session has expired. Please login again.');\n          localStorage.removeItem('studentToken');\n          router.push('/');\n        } else {\n          toast.error(errorMessage || 'Failed to update profile');\n        }\n      }\n    } catch {\n      toast.error('Failed to submit profile information');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Header />\n      <div className=\"space-y-6 p-10 pb-4 md:block\">\n        <div className=\"space-y-0.5\">\n          <h2 className=\"text-2xl font-bold tracking-tight\">\n            Student Profile\n          </h2>\n          <p className=\"text-muted-foreground\">\n            Complete your profile information. Your progress will be automatically saved as you complete each section.\n          </p>\n        </div>\n        <Progress value={progress} className=\"h-2\" />\n        <p className=\"text-sm text-muted-foreground\">\n          {Math.round(progress)}% complete\n        </p>\n\n        <Separator className=\"my-6\" />\n        <div className=\"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0\">\n          <aside className=\"-mx-4 lg:w-1/6 pb-12\">\n            <SidebarNav\n              items={sidebarNavItems}\n              activeSection={activeSection}\n              setActiveSection={setActiveSection}\n            />\n          </aside>\n          <div className=\"flex justify-center w-full\">\n            <div className=\"flex-1 lg:max-w-2xl pb-12\">\n              {profileLoading ? (\n                <div className=\"flex flex-col items-center justify-center py-12\">\n                  <svg\n                    className=\"animate-spin h-10 w-10 text-black mb-4\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n                    <path\n                      className=\"opacity-75\"\n                      fill=\"currentColor\"\n                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    />\n                  </svg>\n                  <p className=\"text-gray-600\">Loading profile information...</p>\n                </div>\n              ) : (\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-lg font-medium\">\n                      {activeSection === 'personal-info' && 'Personal Information'}\n                      {activeSection === 'educational-info' && 'Educational Information'}\n                      {activeSection === 'documents-photo' && 'Documents & Photo'}\n                    </h3>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {activeSection === 'personal-info' && 'Enter your basic personal details and contact information'}\n                      {activeSection === 'educational-info' && 'Select your medium of instruction and classroom standard'}\n                      {activeSection === 'documents-photo' && 'Upload your photo and required documents'}\n                    </p>\n                  </div>\n                  <Separator />\n\n                  <Form {...form}>\n                    <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-8\">\n\n                    {/* Personal Information Section */}\n                    {activeSection === 'personal-info' && (\n                      <div className=\"space-y-6\">\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                          <FormField\n                            control={form.control}\n                            name=\"firstName\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">First Name *</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter First Name\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"middleName\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Middle Name</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Middle Name\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"lastName\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Last Name *</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Last Name\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"mothersName\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Mother's Name</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Mother's Name\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                        </div>\n                        <FormField\n                          control={form.control}\n                          name=\"email\"\n                          render={({ field }) => (\n                            <FormItem>\n                              <FormLabel className=\"text-black font-medium\">Email</FormLabel>\n                              <FormControl>\n                                <Input\n                                  {...field}\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                  placeholder=\"Enter Email\"\n                                  type=\"email\"\n                                />\n                              </FormControl>\n                              <FormMessage className=\"text-red-500\" />\n                            </FormItem>\n                          )}\n                        />\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                          <FormField\n                            control={form.control}\n                            name=\"contact\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Contact Number *</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"8520369851\"\n                                    type=\"tel\"\n                                    inputMode=\"numeric\"\n                                    pattern=\"[0-9]*\"\n                                    onKeyDown={(e) => {\n                                      const specialKeys = [\n                                        'Backspace',\n                                        'Tab',\n                                        'Enter',\n                                        'Escape',\n                                        'Delete',\n                                        'ArrowLeft',\n                                        'ArrowRight',\n                                        'Home',\n                                        'End',\n                                      ];\n                                      if (specialKeys.includes(e.key)) {\n                                        return;\n                                      }\n                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {\n                                        return;\n                                      }\n                                      if (!/^\\d$/.test(e.key)) {\n                                        e.preventDefault();\n                                      }\n                                    }}\n                                    onChange={(e) => {\n                                      const value = e.target.value.replace(/\\D/g, '');\n                                      field.onChange(value);\n                                    }}\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"contact2\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Contact Number 2</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Alternate Number\"\n                                    type=\"tel\"\n                                    inputMode=\"numeric\"\n                                    pattern=\"[0-9]*\"\n                                    onKeyDown={(e) => {\n                                      const specialKeys = [\n                                        'Backspace',\n                                        'Tab',\n                                        'Enter',\n                                        'Escape',\n                                        'Delete',\n                                        'ArrowLeft',\n                                        'ArrowRight',\n                                        'Home',\n                                        'End',\n                                      ];\n                                      if (specialKeys.includes(e.key)) {\n                                        return;\n                                      }\n                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {\n                                        return;\n                                      }\n                                      if (!/^\\d$/.test(e.key)) {\n                                        e.preventDefault();\n                                      }\n                                    }}\n                                    onChange={(e) => {\n                                      const value = e.target.value.replace(/\\D/g, '');\n                                      field.onChange(value);\n                                    }}\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                        </div>\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                          <FormField\n                            control={form.control}\n                            name=\"gender\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Gender</FormLabel>\n                                <Select\n                                  onValueChange={field.onChange}\n                                  value={field.value || undefined}\n                                >\n                                  <FormControl>\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\n                                      <SelectValue placeholder=\"Select\" />\n                                    </SelectTrigger>\n                                  </FormControl>\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\n                                    <SelectItem value=\"male\">Male</SelectItem>\n                                    <SelectItem value=\"female\">Female</SelectItem>\n                                    <SelectItem value=\"other\">Other</SelectItem>\n                                  </SelectContent>\n                                </Select>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"age\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Age</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Age\"\n                                    type=\"number\"\n                                    min=\"1\"\n                                    max=\"100\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"birthday\"\n                            render={({ field }) => (\n                              <FormItem className=\"flex flex-col\">\n                                <FormLabel className=\"text-black font-medium\">Date of Birth</FormLabel>\n                              <Popover>\n                                <PopoverTrigger asChild>\n                                  <FormControl>\n                                    <Button\n                                      variant=\"outline\"\n                                      className={cn(\n                                        'w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg',\n                                        !field.value && 'text-muted-foreground'\n                                      )}\n                                    >\n                                      {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (\n                                        format(field.value, 'PPP')\n                                      ) : (\n                                        <span>Select your birthday</span>\n                                      )}\n                                      <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\n                                    </Button>\n                                  </FormControl>\n                                </PopoverTrigger>\n                                <PopoverContent className=\"w-auto p-0 bg-white border border-gray-300 shadow-lg\" align=\"start\">\n                                  <div className=\"p-3 border-b border-gray-200\">\n                                    <div className=\"flex gap-2 mb-3\">\n                                      <Select\n                                        value={field.value ? field.value.getFullYear().toString() : \"\"}\n                                        onValueChange={(year) => {\n                                          const currentDate = field.value || new Date();\n                                          const newDate = new Date(currentDate);\n                                          newDate.setFullYear(parseInt(year));\n                                          field.onChange(newDate);\n                                        }}\n                                      >\n                                        <SelectTrigger className=\"w-24\">\n                                          <SelectValue placeholder=\"Year\" />\n                                        </SelectTrigger>\n                                        <SelectContent className=\"max-h-48\">\n                                          {Array.from({ length: 125 }, (_, i) => {\n                                            const year = new Date().getFullYear() - i;\n                                            return (\n                                              <SelectItem key={year} value={year.toString()}>\n                                                {year}\n                                              </SelectItem>\n                                            );\n                                          })}\n                                        </SelectContent>\n                                      </Select>\n                                      <Select\n                                        value={field.value ? field.value.getMonth().toString() : \"\"}\n                                        onValueChange={(month) => {\n                                          const currentDate = field.value || new Date();\n                                          const newDate = new Date(currentDate);\n                                          newDate.setMonth(parseInt(month));\n                                          field.onChange(newDate);\n                                        }}\n                                      >\n                                        <SelectTrigger className=\"w-32\">\n                                          <SelectValue placeholder=\"Month\" />\n                                        </SelectTrigger>\n                                        <SelectContent>\n                                          {[\n                                            'January', 'February', 'March', 'April', 'May', 'June',\n                                            'July', 'August', 'September', 'October', 'November', 'December'\n                                          ].map((month, index) => (\n                                            <SelectItem key={index} value={index.toString()}>\n                                              {month}\n                                            </SelectItem>\n                                          ))}\n                                        </SelectContent>\n                                      </Select>\n                                    </div>\n                                  </div>\n                                  <Calendar\n                                    mode=\"single\"\n                                    selected={field.value}\n                                    onSelect={field.onChange}\n                                    disabled={(date) => date > new Date() || date < new Date('1900-01-01')}\n                                    month={field.value || new Date()}\n                                    className=\"rounded-md border-0\"\n                                  />\n                                </PopoverContent>\n                              </Popover>\n                              <FormDescription className=\"text-xs text-gray-500\">\n                                Your date of birth will be verified with your documents\n                              </FormDescription>\n                              <FormMessage className=\"text-red-500\" />\n                            </FormItem>\n                          )}\n                        />\n                        </div>\n                        <FormField\n                          control={form.control}\n                          name=\"address\"\n                          render={({ field }) => (\n                            <FormItem>\n                              <FormLabel className=\"text-black font-medium\">Address</FormLabel>\n                              <FormControl>\n                                <Textarea\n                                  {...field}\n                                  rows={3}\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none\"\n                                  placeholder=\"Enter your full address\"\n                                />\n                              </FormControl>\n                              <FormDescription className=\"text-xs text-gray-500\">\n                                Provide your complete residential address\n                              </FormDescription>\n                              <FormMessage className=\"text-red-500\" />\n                            </FormItem>\n                          )}\n                        />\n                        <FormField\n                          control={form.control}\n                          name=\"school\"\n                          render={({ field }) => (\n                            <FormItem>\n                              <FormLabel className=\"text-black font-medium\">School Name *</FormLabel>\n                              <FormControl>\n                                <Input\n                                  {...field}\n                                  className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                  placeholder=\"Enter School\"\n                                />\n                              </FormControl>\n                              <FormMessage className=\"text-red-500\" />\n                            </FormItem>\n                          )}\n                        />\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                          <FormField\n                            control={form.control}\n                            name=\"aadhaarNumber\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Aadhaar Number</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Aadhaar No\"\n                                    type=\"tel\"\n                                    inputMode=\"numeric\"\n                                    pattern=\"[0-9]*\"\n                                    maxLength={12}\n                                    onKeyDown={(e) => {\n                                      const specialKeys = [\n                                        'Backspace',\n                                        'Tab',\n                                        'Enter',\n                                        'Escape',\n                                        'Delete',\n                                        'ArrowLeft',\n                                        'ArrowRight',\n                                        'Home',\n                                        'End',\n                                      ];\n                                      if (specialKeys.includes(e.key)) {\n                                        return;\n                                      }\n                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {\n                                        return;\n                                      }\n                                      if (!/^\\d$/.test(e.key)) {\n                                        e.preventDefault();\n                                      }\n                                    }}\n                                    onChange={(e) => {\n                                      const value = e.target.value.replace(/\\D/g, '');\n                                      field.onChange(value);\n                                    }}\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"bloodGroup\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Blood Group</FormLabel>\n                                <Select\n                                  onValueChange={field.onChange}\n                                  value={field.value || undefined}\n                                >\n                                  <FormControl>\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\n                                      <SelectValue placeholder=\"Select\" />\n                                    </SelectTrigger>\n                                  </FormControl>\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\n                                    <SelectItem value=\"A+\">A+</SelectItem>\n                                    <SelectItem value=\"A-\">A-</SelectItem>\n                                    <SelectItem value=\"B+\">B+</SelectItem>\n                                    <SelectItem value=\"B-\">B-</SelectItem>\n                                    <SelectItem value=\"AB+\">AB+</SelectItem>\n                                    <SelectItem value=\"AB-\">AB-</SelectItem>\n                                    <SelectItem value=\"O+\">O+</SelectItem>\n                                    <SelectItem value=\"O-\">O-</SelectItem>\n                                  </SelectContent>\n                                </Select>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"birthPlace\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Birth Place</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Birth Place\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"motherTongue\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Mother Tongue</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Mother Tongue\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"religion\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Religion</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Religion\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"caste\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Caste</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Caste\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"subCaste\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Sub Caste</FormLabel>\n                                <FormControl>\n                                  <Input\n                                    {...field}\n                                    className=\"bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg\"\n                                    placeholder=\"Enter Sub Caste\"\n                                  />\n                                </FormControl>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Educational Information Section */}\n                    {activeSection === 'educational-info' && (\n                      <div className=\"space-y-6\">\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                          <FormField\n                            control={form.control}\n                            name=\"medium\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Medium *</FormLabel>\n                                <Select\n                                  onValueChange={(value) => {\n                                    field.onChange(value);\n                                  }}\n                                  value={field.value || undefined}\n                                >\n                                  <FormControl>\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\n                                      <SelectValue placeholder=\"Select\" />\n                                    </SelectTrigger>\n                                  </FormControl>\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\n                                    <SelectItem value=\"english\">English</SelectItem>\n                                    <SelectItem value=\"gujarati\">Gujarati</SelectItem>\n                                  </SelectContent>\n                                </Select>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                          <FormField\n                            control={form.control}\n                            name=\"classroom\"\n                            render={({ field }) => (\n                              <FormItem>\n                                <FormLabel className=\"text-black font-medium\">Standard *</FormLabel>\n                                <Select\n                                  onValueChange={(value) => {\n                                    field.onChange(value);\n                                  }}\n                                  value={field.value || undefined}\n                                >\n                                  <FormControl>\n                                    <SelectTrigger className=\"bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full\">\n                                      <SelectValue placeholder=\"Select\" />\n                                    </SelectTrigger>\n                                  </FormControl>\n                                  <SelectContent className=\"bg-white w-[var(--radix-select-trigger-width)]\">\n                                    {profileLoading ? (\n                                      <div className=\"flex items-center justify-center p-4\">\n                                        <svg\n                                          className=\"animate-spin h-5 w-5 text-black\"\n                                          xmlns=\"http://www.w3.org/2000/svg\"\n                                          fill=\"none\"\n                                          viewBox=\"0 0 24 24\"\n                                        >\n                                          <circle\n                                            className=\"opacity-25\"\n                                            cx=\"12\"\n                                            cy=\"12\"\n                                            r=\"10\"\n                                            stroke=\"currentColor\"\n                                            strokeWidth=\"4\"\n                                          />\n                                          <path\n                                            className=\"opacity-75\"\n                                            fill=\"currentColor\"\n                                            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                          />\n                                        </svg>\n                                      </div>\n                                    ) : classroomOptions.length > 0 ? (\n                                      classroomOptions.map((option) => (\n                                        <SelectItem key={option.id} value={option.value}>\n                                          {option.value}\n                                        </SelectItem>\n                                      ))\n                                    ) : (\n                                      <div className=\"p-2 text-center text-gray-500\">No classroom options available</div>\n                                    )}\n                                  </SelectContent>\n                                </Select>\n                                <FormMessage className=\"text-red-500\" />\n                              </FormItem>\n                            )}\n                          />\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Documents & Photo Section */}\n                    {activeSection === 'documents-photo' && (\n                      <div className=\"space-y-6\">\n\n                        <FormField\n                          control={form.control}\n                          name=\"photo\"\n                          render={() => (\n                            <Card className=\"shadow-lg border-0\">\n                              <CardHeader className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg\">\n                                <CardTitle className=\"text-lg font-medium text-gray-800\">Student Image</CardTitle>\n                                <CardDescription className=\"text-gray-600\">\n                                  Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)\n                                </CardDescription>\n                              </CardHeader>\n                          <CardContent>\n                            <FormItem>\n                              <FormControl>\n                                <div>\n                                  {cameraError && (\n                                    <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n                                      <p className=\"text-red-700 text-sm\">{cameraError}</p>\n                                    </div>\n                                  )}\n                                  {!isCameraOpen && !photo && (\n                                    <Button\n                                      type=\"button\"\n                                      onClick={openCamera}\n                                      className=\"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2\"\n                                    >\n                                      <Camera className=\"h-5 w-5 mr-2\" />\n                                      Open Camera\n                                    </Button>\n                                  )}\n                                  {isCameraOpen && (\n                                    <div className=\"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm\">\n                                      <video\n                                        ref={videoRef}\n                                        autoPlay\n                                        playsInline\n                                        className=\"w-full h-auto transform scale-x-[-1]\"\n                                      />\n                                      <div className=\"flex p-4 bg-gray-50\">\n                                        <Button\n                                          type=\"button\"\n                                          onClick={capturePhoto}\n                                          variant=\"default\"\n                                          className=\"flex-1 mr-2 bg-black hover:bg-gray-800 text-white\"\n                                        >\n                                          <Check className=\"h-4 w-4 mr-2\" />\n                                          Capture\n                                        </Button>\n                                        <Button\n                                          type=\"button\"\n                                          onClick={closeCamera}\n                                          variant=\"outline\"\n                                          className=\"flex-1 border-gray-300\"\n                                        >\n                                          <X className=\"h-4 w-4 mr-2\" />\n                                          Cancel\n                                        </Button>\n                                      </div>\n                                    </div>\n                                  )}\n                                  {!isCameraOpen && (profileData?.profile?.photo || photo) && (\n                                    <div className=\"flex flex-col sm:flex-row items-center gap-4\">\n                                      <div className=\"border rounded-lg shadow-md bg-gray-50 p-4 max-w-full\">\n                                        <div className=\"flex justify-center\">\n                                          {(() => {\n                                            const displayPhoto = photo || profileData?.profile?.photo;\n                                            if (displayPhoto) {\n                                              return (\n                                                <Image\n                                                  src={\n                                                    displayPhoto.startsWith('data:')\n                                                      ? displayPhoto\n                                                      : displayPhoto.startsWith('http')\n                                                      ? displayPhoto\n                                                      : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`\n                                                  }\n                                                  alt=\"Student Photo\"\n                                                  height={1000}\n                                                  width={1000}\n                                                  className=\"max-w-full max-h-80 object-contain rounded-lg\"\n                                                  style={{ height: 'auto', width: 'auto' }}\n                                                  unoptimized={displayPhoto.startsWith('data:')}\n                                                />\n                                              );\n                                            }\n                                            return (\n                                              <div className=\"flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg\">\n                                                <Camera className=\"h-12 w-12 text-gray-400\" />\n                                              </div>\n                                            );\n                                          })()}\n                                        </div>\n                                      </div>\n                                      <Button\n                                        type=\"button\"\n                                        onClick={() => {\n                                          setPhoto(null);\n                                          setCameraError(null);\n                                          dispatch(updateProfilePhoto(undefined));\n                                          form.setValue('photo', null);\n                                          openCamera();\n                                        }}\n                                        variant=\"outline\"\n                                        className=\"border-gray-300\"\n                                      >\n                                        <Camera className=\"h-4 w-4 mr-2\" />\n                                        Retake Photo\n                                      </Button>\n                                    </div>\n                                  )}\n                                  <canvas ref={canvasRef} style={{ display: 'none' }} />\n                                </div>\n                              </FormControl>\n                              <FormDescription className=\"text-xs text-gray-500 mt-2\">\n                                A clear photo helps us identify you and personalize your profile\n                              </FormDescription>\n                              <FormMessage className=\"text-red-500\" />\n                            </FormItem>\n                          </CardContent>\n                        </Card>\n                      )}\n                    />\n                    <FormField\n                      control={form.control}\n                      name=\"document\"\n                      render={({ field }) => (\n                        <Card className=\"shadow-lg border-0\">\n                          <CardHeader className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg\">\n                            <CardTitle className=\"text-lg font-medium text-gray-800\">Identity Document</CardTitle>\n                            <CardDescription className=\"text-gray-600\">\n                              Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate\n                            </CardDescription>\n                          </CardHeader>\n                          <CardContent>\n                            <FormItem>\n                              {!uploadedDocument ? (\n                                <FormControl>\n                                  <div className=\"flex items-center justify-center w-full\">\n                                    <label className=\"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors\">\n                                      <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                                        <Upload className=\"w-10 h-10 mb-3 text-black\" />\n                                        <p className=\"mb-2 text-sm text-gray-700\">\n                                          <span className=\"font-semibold\">Click to upload</span> or drag and drop\n                                        </p>\n                                        <p className=\"text-xs text-gray-500\">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>\n                                      </div>\n                                      <Input\n                                        id=\"document\"\n                                        type=\"file\"\n                                        accept=\".pdf,.jpg,.jpeg,.png\"\n                                        className=\"hidden\"\n                                        onChange={(e) => {\n                                          const file = e.target.files?.[0];\n                                          if (file) {\n                                            if (file.size > 5 * 1024 * 1024) {\n                                              toast.error('File size exceeds 5MB limit');\n                                              return;\n                                            }\n                                            const documentWithUrl = {\n                                              name: file.name,\n                                              size: file.size,\n                                              type: file.type,\n                                              url: URL.createObjectURL(file),\n                                            };\n                                            setUploadedDocument(documentWithUrl);\n                                            setIsDocumentRemoved(false);\n                                            field.onChange(file);\n                                          }\n                                        }}\n                                      />\n                                    </label>\n                                  </div>\n                                </FormControl>\n                              ) : (\n                                <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\n                                  <div className=\"flex items-center justify-between\">\n                                    <div className=\"flex items-center space-x-3\">\n                                      <div className=\"p-2 bg-[#fff8f3] rounded-full\">\n                                        <FileText className=\"h-5 w-5 text-black\" />\n                                      </div>\n                                      <div>\n                                        <p className=\"text-sm font-medium text-gray-700\">{uploadedDocument.name}</p>\n                                        <p className=\"text-xs text-gray-500\">\n                                          {uploadedDocument instanceof File\n                                            ? formatFileSize(uploadedDocument.size)\n                                            : 'Previously uploaded document'}\n                                        </p>\n                                      </div>\n                                    </div>\n                                    <div className=\"flex space-x-2\">\n                                      {uploadedDocument && 'url' in uploadedDocument && (\n                                        <Button\n                                          type=\"button\"\n                                          variant=\"outline\"\n                                          size=\"sm\"\n                                          onClick={() => window.open(uploadedDocument.url, '_blank')}\n                                          className=\"h-8 px-3 border-gray-200\"\n                                        >\n                                          View\n                                        </Button>\n                                      )}\n                                      <Button\n                                        type=\"button\"\n                                        variant=\"outline\"\n                                        size=\"sm\"\n                                        onClick={removeDocument}\n                                        className=\"h-8 w-8 p-0 border-gray-200\"\n                                      >\n                                        <X className=\"h-4 w-4 text-gray-500\" />\n                                      </Button>\n                                    </div>\n                                  </div>\n                                </div>\n                              )}\n                              <FormDescription className=\"text-xs text-gray-500 mt-2\">\n                                This document will serve to verify your identity and date of birth.\n                              </FormDescription>\n                              <FormMessage className=\"text-red-500\" />\n                            </FormItem>\n                          </CardContent>\n                        </Card>\n                      )}\n                    />\n                      </div>\n                    )}\n\n                    {/* Submit Button */}\n                    <div className=\"flex justify-end pt-6 border-t border-gray-100\">\n                      <Button\n                        type=\"submit\"\n                        disabled={isSubmitting}\n                        className=\"bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200\"\n                      >\n                        {isSubmitting ? (\n                          <div className=\"flex items-center gap-2\">\n                            <svg className=\"animate-spin h-4 w-4\" viewBox=\"0 0 24 24\">\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n                            </svg>\n                            Saving...\n                          </div>\n                        ) : profileData ? (\n                          'Update Profile'\n                        ) : (\n                          'Save Profile'\n                        )}\n                      </Button>\n                    </div>\n\n                    </form>\n                  </Form>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n      <Footer />\n    </>\n  );\n};\n\nconst StudentProfilePage = () => {\n  return (\n    <Suspense\n      fallback={\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <svg\n            className=\"animate-spin h-10 w-10 text-black\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        </div>\n      }\n    >\n      <StudentProfileContent />\n    </Suspense>\n  );\n};\n\nexport default StudentProfilePage;"], "names": [], "mappings": ";;;AA8OsB;;AA5OtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AASA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA1CA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,MAAM,oBAAoB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,uCAAuC,QAAQ,GAAG,EAAE,CAAC,uIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACvF,SAAS,uIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,IAAI,6CACR,KAAK,CAAC,SAAS;IAClB,UAAU,uIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,IAAI,6CACR,KAAK,CAAC,SAAS,4CACf,QAAQ,GACR,EAAE,CAAC,uIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,uIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAE,gBAAgB;IAAmC;IACtE,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,KAAK,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACxB,eAAe,uIAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,IAAI,qCACR,GAAG,CAAC,IAAI,qCACR,KAAK,CAAC,SAAS,4CACf,QAAQ,GACR,EAAE,CAAC,uIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,uIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;IACvB,UAAU,uIAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;AAC5B;AAIA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,wBAAwB;;IAC5B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,aAAa,GAAG,CAAC,YAAY;IAC9C,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAErD;IACF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,EAAE,WAAW,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;6CACzD,CAAC,QAAqB,MAAM,cAAc;;IAG5C,MAAM,UAAU,aAAa,WAAW;IACxC,MAAM,mBAAmB,aAAa,oBAAoB,EAAE;IAE5D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAqB;QACtC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW;YACX,YAAY;YACZ,UAAU;YACV,aAAa;YACb,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,KAAK;YACL,eAAe;YACf,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA,MAAM;IACR;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,CAAC,cAAc;gBACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd;QACF;0CAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;YAC7B;QACF;0CAAG;QAAC;KAAS;IAEb,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,aAAa,SAAS;gBACxB,MAAM,UAAU,YAAY,OAAO;gBACnC,IAAI,oBAAoB;gBACxB,MAAM,gBAAgB;gBAEtB,iCAAiC;gBACjC,IAAI,QAAQ,OAAO,EAAE,aAAa,QAAQ,OAAO,EAAE,YAAY,QAAQ,OAAO,EAAE,WAC5E,QAAQ,QAAQ,IAAI,QAAQ,MAAM,IAAI,QAAQ,OAAO,EAAE;oBACzD;gBACF;gBAEA,oCAAoC;gBACpC,IAAI,QAAQ,MAAM,IAAI,QAAQ,SAAS,EAAE;oBACvC;gBACF;gBAEA,qCAAqC;gBACrC,IAAI,QAAQ,KAAK,IAAI,QAAQ,WAAW,EAAE;oBACxC;gBACF;gBAEA,YAAY,AAAC,oBAAoB,gBAAiB;YACpD;QACF;0CAAG;QAAC;KAAY;IAEhB,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,CAAC,aAAa;YAElB,MAAM,aAAa,YAAY,OAAO;YACtC,MAAM,cAAc,YAAY,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;YAE9F,MAAM,aAAa;gBACjB,WAAW,aAAa,aAAa;gBACrC,YAAY,aAAa,cAAc;gBACvC,UAAU,aAAa,YAAY;gBACnC,aAAa,aAAa,eAAe;gBACzC,OAAO,aAAa,SAAS;gBAC7B,SAAS,aAAa,WAAW;gBACjC,UAAU,YAAY,cAAc;gBACpC,QAAQ,YAAY,UAAU;gBAC9B,WAAW,YAAY,aAAa;gBACpC,QAAQ,YAAY,UAAU;gBAC9B,UAAU,YAAY,WAAW,IAAI,KAAK,WAAW,QAAQ,IAAI;gBACjE,QAAQ,YAAY,UAAU;gBAC9B,SAAS,YAAY,WAAW;gBAChC,KAAK,YAAY,KAAK,cAAc;gBACpC,eAAe,YAAY,aAAa;gBACxC,YAAY,YAAY,cAAc;gBACtC,YAAY,YAAY,cAAc;gBACtC,cAAc,YAAY,gBAAgB;gBAC1C,UAAU,YAAY,YAAY;gBAClC,OAAO,YAAY,SAAS;gBAC5B,UAAU,YAAY,YAAY;YACpC;YAEA,IAAI,YAAY,SAAS,CAAC,OAAO;gBAC/B,SAAS,WAAW,KAAK;gBACzB,KAAK,QAAQ,CAAC,SAAS,WAAW,KAAK;YACzC;YAEA,IAAI,YAAY,eAAe,CAAC,oBAAoB,CAAC,mBAAmB;gBACtE,MAAM,UAAU,8DAAwC;gBACxD,MAAM,cAAc,WAAW,WAAW,CAAC,UAAU,CAAC,UAClD,WAAW,WAAW,GACtB,GAAG,UAAU,WAAW,WAAW,EAAE;gBAEzC,MAAM,cAAc;oBAClB,MAAM,YAAY,KAAK,CAAC,KAAK,GAAG,MAAM;oBACtC,MAAM;oBACN,KAAK;oBACL,MAAM;gBACR;gBAEA,oBAAoB;gBACpB,KAAK,QAAQ,CAAC,YAAY;YAC5B;YAEA,MAAM,gBAAgB,KAAK,SAAS;YACpC,MAAM,cAAc,CAAC,cAAc,SAAS,IAAI,CAAC,cAAc,QAAQ,IAAI,CAAC,cAAc,OAAO;YACjG,MAAM,2BAA2B,CAAC,cAAc,MAAM,IAAI,CAAC,cAAc,SAAS;YAElF,IAAI,eAAe,0BAA0B;gBAC3C,KAAK,KAAK,CAAC;YACb;QACF;0CAAG;QAAC;QAAa;QAAM;QAAO;QAAkB;KAAkB;IAElE,MAAM,aAAa;QACjB,eAAe;QAEf,IAAI;YACF,IAAI,CAAC,UAAU,YAAY,EAAE,cAAc;gBACzC,MAAM,IAAI,MAAM;YAClB;YAEA,gBAAgB;YAEhB,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBAAE,YAAY;gBAAO;YAC9B;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,SAAS,OAAO,CAAC,gBAAgB,GAAG;oBAClC,SAAS,OAAO,EAAE,OAAO,MAAM,IAAM,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACnD;YACF;QACF,EAAE,OAAO,OAAY;YACnB,gBAAgB;YAChB,MAAM,UAAU,MAAM,IAAI,KAAK,oBAC3B,yDACA;YACJ,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC,QAA2B,WAAmB,GAAG,EAAE,UAAkB,GAAG;QAC7F,MAAM,UAAU,OAAO,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,gBAAgB,OAAO,KAAK;QAClC,MAAM,iBAAiB,OAAO,MAAM;QAEpC,IAAI,WAAW;QACf,IAAI,YAAY;QAEhB,IAAI,gBAAgB,UAAU;YAC5B,WAAW;YACX,YAAY,AAAC,iBAAiB,WAAY;QAC5C;QAEA,MAAM,mBAAmB,SAAS,aAAa,CAAC;QAChD,iBAAiB,KAAK,GAAG;QACzB,iBAAiB,MAAM,GAAG;QAE1B,MAAM,oBAAoB,iBAAiB,UAAU,CAAC;QACtD,IAAI,CAAC,mBAAmB,OAAO;QAE/B,kBAAkB,SAAS,CAAC,QAAQ,GAAG,GAAG,UAAU;QAEpD,OAAO,iBAAiB,SAAS,CAAC,cAAc;IAClD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;QAE7C,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,UAAU,OAAO,UAAU,CAAC;QAElC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,SAAS,UAAU,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACpD,SAAS;QACT,SAAS,MAAM,CAAC,GAAG;QACnB,SAAS,UAAU,OAAO,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACvE,SAAS;QAET,MAAM,yBAAyB,cAAc,QAAQ,KAAK;QAC1D,MAAM,aAAa,uBAAuB,KAAK,CAAC,IAAI,CAAC,EAAE;QACvD,MAAM,WAAW,AAAC,WAAW,MAAM,GAAG,IAAK,IAAI;QAE/C,IAAI,WAAW,MAAM;YACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,SAAS;QACT,KAAK,QAAQ,CAAC,SAAS;QACvB,SAAS,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE;QAE5B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,SAAS,OAAO,EAAE,WAAW;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;QACA,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,IAAI,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAU;YAC7F,IAAI,eAAe,CAAC,iBAAiB,GAAG;QAC1C;QAEA,oBAAoB;QACpB,qBAAqB;QACrB,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,WAAW,UAAU,KAAK,GAAG;QACjC,KAAK,QAAQ,CAAC,YAAY;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,MAAM,OAAO,QAAQ;aAC5B,IAAI,QAAQ,SAAS,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK;aACxD,OAAO,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;IAC7C;IAIA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,MAAM,eAAe,SAAS,aAAa,SAAS;YACpD,IAAI,CAAC,cAAc;gBACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,IAAI,CAAC,oBAAoB,mBAAmB;gBAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,IAAI,CAAE,MAAM,KAAK,OAAO,IAAK;gBAC3B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,MAAM,WAAgB;gBACpB,WAAW,KAAK,SAAS;gBACzB,YAAY,KAAK,UAAU;gBAC3B,UAAU,KAAK,QAAQ;gBACvB,aAAa,KAAK,WAAW;gBAC7B,OAAO,KAAK,KAAK;gBACjB,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,SAAS;gBACzB,QAAQ,KAAK,MAAM;gBACnB,UAAU,KAAK,QAAQ,EAAE,iBAAiB;gBAC1C,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO;gBACrB,KAAK,KAAK,GAAG;gBACb,eAAe,KAAK,aAAa;gBACjC,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,cAAc,KAAK,YAAY;gBAC/B,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;YAEA,IAAI,OAAO,WAAW,UAAU;gBAC9B,MAAM,aAAa,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtC,MAAM,WAAW,AAAC,WAAW,MAAM,GAAG,IAAK,IAAI;gBAE/C,IAAI,WAAW,MAAM;oBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBAEA,SAAS,KAAK,GAAG;gBACjB,SAAS,aAAa,GAAG;YAC3B;YAEA,IAAI,4BAA4B,QAAS,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAW;gBACnI,MAAM,eAAe,4BAA4B,OAC7C,mBACA,MAAM,MAAM,iBAAiB,GAAG,EAC7B,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA,OAAQ,IAAI,KAAK;wBAAC;qBAAK,EAAE,iBAAiB,IAAI,EAAE;wBAAE,MAAM,iBAAiB,IAAI;oBAAC;gBAE1F,MAAM,iBAAiB,MAAM,IAAI,QAAgB,CAAC,SAAS;oBACzD,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,AAAC,OAAO,MAAM,CAAY,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrE,OAAO,OAAO,GAAG;oBACjB,OAAO,aAAa,CAAC;gBACvB;gBAEA,MAAM,YAAY,AAAC,eAAe,MAAM,GAAG,IAAK,IAAI;gBACpD,IAAI,YAAY,MAAM;oBACpB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;gBACF;gBAEA,SAAS,QAAQ,GAAG;gBACpB,SAAS,gBAAgB,GAAG,aAAa,IAAI;gBAC7C,SAAS,YAAY,GAAG,aAAa,IAAI;YAC3C;YAEA,0BAA0B;YAC1B,IAAI,qBAAqB,aAAa,SAAS,aAAa;gBAC1D,SAAS,cAAc,GAAG;YAC5B;YAEA,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,CAAC,cAAc;gBACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,CAAA,GAAA,iJAAA,CAAA,uBAAoB,AAAD,EAAE;YAEnD,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;gBAC7C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU,YAAY,UAAU,cAAc,CAAC;gBAExE,MAAM,sBAAsB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;gBAC/E,MAAM,cAAc;oBAClB,GAAG,mBAAmB;oBACtB,IAAI,oBAAoB,EAAE,IAAI,aAAa,SAAS,SAAS,MAAM;oBACnE,WAAW,KAAK,SAAS;oBACzB,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,aAAa,KAAK,WAAW;oBAC7B,OAAO,KAAK,KAAK,IAAI,oBAAoB,KAAK,IAAI,aAAa,SAAS,SAAS,SAAS;oBAC1F,SAAS,KAAK,OAAO;gBACvB;gBAEA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBACpD,qBAAqB;gBACrB,MAAM,SAAS,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD;gBAEjC,IAAI,UAAU;oBACZ,IAAI,QAAQ;wBACV,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ;oBACrC,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,YAAY;gBACnD,MAAM,eAAe,OAAO,OAAO;gBAEnC,IAAI,aAAa,QAAQ,CAAC,UAAU,aAAa,QAAQ,CAAC,iBAAiB;oBACzE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,aAAa,UAAU,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;gBAC9B;YACF;QACF,EAAE,OAAM;YACN,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,6LAAC,sIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAU,WAAU;;;;;;kCACrC,6LAAC;wBAAE,WAAU;;4BACV,KAAK,KAAK,CAAC;4BAAU;;;;;;;kCAGxB,6LAAC,wIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC,oKAAA,CAAA,aAAU;oCACT,OAAO;oCACP,eAAe;oCACf,kBAAkB;;;;;;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAM;gDACN,MAAK;gDACL,SAAQ;;kEAER,6LAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,6LAAC;wDACC,WAAU;wDACV,MAAK;wDACL,GAAE;;;;;;;;;;;;0DAGN,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;6DAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;4DACX,kBAAkB,mBAAmB;4DACrC,kBAAkB,sBAAsB;4DACxC,kBAAkB,qBAAqB;;;;;;;kEAE1C,6LAAC;wDAAE,WAAU;;4DACV,kBAAkB,mBAAmB;4DACrC,kBAAkB,sBAAsB;4DACxC,kBAAkB,qBAAqB;;;;;;;;;;;;;0DAG5C,6LAAC,wIAAA,CAAA,YAAS;;;;;0DAEV,6LAAC,mIAAA,CAAA,OAAI;gDAAE,GAAG,IAAI;0DACZ,cAAA,6LAAC;oDAAK,UAAU,KAAK,YAAY,CAAC;oDAAW,WAAU;;wDAGtD,kBAAkB,iCACjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAK/B,6LAAC,mIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8FACP,6LAAC,mIAAA,CAAA,YAAS;oFAAC,WAAU;8FAAyB;;;;;;8FAC9C,6LAAC,mIAAA,CAAA,cAAW;8FACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wFACH,GAAG,KAAK;wFACT,WAAU;wFACV,aAAY;wFACZ,MAAK;;;;;;;;;;;8FAGT,6LAAC,mIAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;8EAI7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,WAAU;gGACV,SAAQ;gGACR,WAAW,CAAC;oGACV,MAAM,cAAc;wGAClB;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;qGACD;oGACD,IAAI,YAAY,QAAQ,CAAC,EAAE,GAAG,GAAG;wGAC/B;oGACF;oGACA,IAAI,EAAE,OAAO,IAAI;wGAAC;wGAAK;wGAAK;wGAAK;qGAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAAK;wGACnE;oGACF;oGACA,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG;wGACvB,EAAE,cAAc;oGAClB;gGACF;gGACA,UAAU,CAAC;oGACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;oGAC5C,MAAM,QAAQ,CAAC;gGACjB;;;;;;;;;;;sGAGJ,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,WAAU;gGACV,SAAQ;gGACR,WAAW,CAAC;oGACV,MAAM,cAAc;wGAClB;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;qGACD;oGACD,IAAI,YAAY,QAAQ,CAAC,EAAE,GAAG,GAAG;wGAC/B;oGACF;oGACA,IAAI,EAAE,OAAO,IAAI;wGAAC;wGAAK;wGAAK;wGAAK;qGAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAAK;wGACnE;oGACF;oGACA,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG;wGACvB,EAAE,cAAc;oGAClB;gGACF;gGACA,UAAU,CAAC;oGACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;oGAC5C,MAAM,QAAQ,CAAC;gGACjB;;;;;;;;;;;sGAGJ,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAK/B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,qIAAA,CAAA,SAAM;4FACL,eAAe,MAAM,QAAQ;4FAC7B,OAAO,MAAM,KAAK,IAAI;;8GAEtB,6LAAC,mIAAA,CAAA,cAAW;8GACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wGAAC,WAAU;kHACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4GAAC,aAAY;;;;;;;;;;;;;;;;8GAG7B,6LAAC,qIAAA,CAAA,gBAAa;oGAAC,WAAU;;sHACvB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAO;;;;;;sHACzB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAS;;;;;;sHAC3B,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAQ;;;;;;;;;;;;;;;;;;sGAG9B,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,KAAI;gGACJ,KAAI;;;;;;;;;;;sGAGR,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;oFAAC,WAAU;;sGAClB,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAChD,6LAAC,sIAAA,CAAA,UAAO;;8GACN,6LAAC,sIAAA,CAAA,iBAAc;oGAAC,OAAO;8GACrB,cAAA,6LAAC,mIAAA,CAAA,cAAW;kHACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4GACL,SAAQ;4GACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iGACA,CAAC,MAAM,KAAK,IAAI;;gHAGjB,MAAM,KAAK,IAAI,MAAM,KAAK,YAAY,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,MACvE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,uBAEpB,6LAAC;8HAAK;;;;;;8HAER,6LAAC,6MAAA,CAAA,WAAY;oHAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8GAI9B,6LAAC,sIAAA,CAAA,iBAAc;oGAAC,WAAU;oGAAuD,OAAM;;sHACrF,6LAAC;4GAAI,WAAU;sHACb,cAAA,6LAAC;gHAAI,WAAU;;kIACb,6LAAC,qIAAA,CAAA,SAAM;wHACL,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,KAAK;wHAC5D,eAAe,CAAC;4HACd,MAAM,cAAc,MAAM,KAAK,IAAI,IAAI;4HACvC,MAAM,UAAU,IAAI,KAAK;4HACzB,QAAQ,WAAW,CAAC,SAAS;4HAC7B,MAAM,QAAQ,CAAC;wHACjB;;0IAEA,6LAAC,qIAAA,CAAA,gBAAa;gIAAC,WAAU;0IACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oIAAC,aAAY;;;;;;;;;;;0IAE3B,6LAAC,qIAAA,CAAA,gBAAa;gIAAC,WAAU;0IACtB,MAAM,IAAI,CAAC;oIAAE,QAAQ;gIAAI,GAAG,CAAC,GAAG;oIAC/B,MAAM,OAAO,IAAI,OAAO,WAAW,KAAK;oIACxC,qBACE,6LAAC,qIAAA,CAAA,aAAU;wIAAY,OAAO,KAAK,QAAQ;kJACxC;uIADc;;;;;gIAIrB;;;;;;;;;;;;kIAGJ,6LAAC,qIAAA,CAAA,SAAM;wHACL,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,GAAG,QAAQ,KAAK;wHACzD,eAAe,CAAC;4HACd,MAAM,cAAc,MAAM,KAAK,IAAI,IAAI;4HACvC,MAAM,UAAU,IAAI,KAAK;4HACzB,QAAQ,QAAQ,CAAC,SAAS;4HAC1B,MAAM,QAAQ,CAAC;wHACjB;;0IAEA,6LAAC,qIAAA,CAAA,gBAAa;gIAAC,WAAU;0IACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oIAAC,aAAY;;;;;;;;;;;0IAE3B,6LAAC,qIAAA,CAAA,gBAAa;0IACX;oIACC;oIAAW;oIAAY;oIAAS;oIAAS;oIAAO;oIAChD;oIAAQ;oIAAU;oIAAa;oIAAW;oIAAY;iIACvD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,6LAAC,qIAAA,CAAA,aAAU;wIAAa,OAAO,MAAM,QAAQ;kJAC1C;uIADc;;;;;;;;;;;;;;;;;;;;;;;;;;;sHAQ3B,6LAAC,uIAAA,CAAA,WAAQ;4GACP,MAAK;4GACL,UAAU,MAAM,KAAK;4GACrB,UAAU,MAAM,QAAQ;4GACxB,UAAU,CAAC,OAAS,OAAO,IAAI,UAAU,OAAO,IAAI,KAAK;4GACzD,OAAO,MAAM,KAAK,IAAI,IAAI;4GAC1B,WAAU;;;;;;;;;;;;;;;;;;sGAIhB,6LAAC,mIAAA,CAAA,kBAAe;4FAAC,WAAU;sGAAwB;;;;;;sGAGnD,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAK7B,6LAAC,mIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8FACP,6LAAC,mIAAA,CAAA,YAAS;oFAAC,WAAU;8FAAyB;;;;;;8FAC9C,6LAAC,mIAAA,CAAA,cAAW;8FACV,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wFACN,GAAG,KAAK;wFACT,MAAM;wFACN,WAAU;wFACV,aAAY;;;;;;;;;;;8FAGhB,6LAAC,mIAAA,CAAA,kBAAe;oFAAC,WAAU;8FAAwB;;;;;;8FAGnD,6LAAC,mIAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;8EAI7B,6LAAC,mIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8FACP,6LAAC,mIAAA,CAAA,YAAS;oFAAC,WAAU;8FAAyB;;;;;;8FAC9C,6LAAC,mIAAA,CAAA,cAAW;8FACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wFACH,GAAG,KAAK;wFACT,WAAU;wFACV,aAAY;;;;;;;;;;;8FAGhB,6LAAC,mIAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;;;;;;8EAI7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;gGACZ,MAAK;gGACL,WAAU;gGACV,SAAQ;gGACR,WAAW;gGACX,WAAW,CAAC;oGACV,MAAM,cAAc;wGAClB;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;wGACA;qGACD;oGACD,IAAI,YAAY,QAAQ,CAAC,EAAE,GAAG,GAAG;wGAC/B;oGACF;oGACA,IAAI,EAAE,OAAO,IAAI;wGAAC;wGAAK;wGAAK;wGAAK;qGAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAAK;wGACnE;oGACF;oGACA,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,GAAG;wGACvB,EAAE,cAAc;oGAClB;gGACF;gGACA,UAAU,CAAC;oGACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;oGAC5C,MAAM,QAAQ,CAAC;gGACjB;;;;;;;;;;;sGAGJ,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,qIAAA,CAAA,SAAM;4FACL,eAAe,MAAM,QAAQ;4FAC7B,OAAO,MAAM,KAAK,IAAI;;8GAEtB,6LAAC,mIAAA,CAAA,cAAW;8GACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wGAAC,WAAU;kHACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4GAAC,aAAY;;;;;;;;;;;;;;;;8GAG7B,6LAAC,qIAAA,CAAA,gBAAa;oGAAC,WAAU;;sHACvB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAM;;;;;;sHACxB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAM;;;;;;sHACxB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,6LAAC,qIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;;;;;;;;;;;;;sGAG3B,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;sFAI7B,6LAAC,mIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;sGACP,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAyB;;;;;;sGAC9C,6LAAC,mIAAA,CAAA,cAAW;sGACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;gGACH,GAAG,KAAK;gGACT,WAAU;gGACV,aAAY;;;;;;;;;;;sGAGhB,6LAAC,mIAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wDASlC,kBAAkB,oCACjB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,mIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;kGACP,6LAAC,mIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,6LAAC,qIAAA,CAAA,SAAM;wFACL,eAAe,CAAC;4FACd,MAAM,QAAQ,CAAC;wFACjB;wFACA,OAAO,MAAM,KAAK,IAAI;;0GAEtB,6LAAC,mIAAA,CAAA,cAAW;0GACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oGAAC,WAAU;8GACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wGAAC,aAAY;;;;;;;;;;;;;;;;0GAG7B,6LAAC,qIAAA,CAAA,gBAAa;gGAAC,WAAU;;kHACvB,6LAAC,qIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAU;;;;;;kHAC5B,6LAAC,qIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAW;;;;;;;;;;;;;;;;;;kGAGjC,6LAAC,mIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;kFAI7B,6LAAC,mIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;kGACP,6LAAC,mIAAA,CAAA,YAAS;wFAAC,WAAU;kGAAyB;;;;;;kGAC9C,6LAAC,qIAAA,CAAA,SAAM;wFACL,eAAe,CAAC;4FACd,MAAM,QAAQ,CAAC;wFACjB;wFACA,OAAO,MAAM,KAAK,IAAI;;0GAEtB,6LAAC,mIAAA,CAAA,cAAW;0GACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oGAAC,WAAU;8GACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;wGAAC,aAAY;;;;;;;;;;;;;;;;0GAG7B,6LAAC,qIAAA,CAAA,gBAAa;gGAAC,WAAU;0GACtB,+BACC,6LAAC;oGAAI,WAAU;8GACb,cAAA,6LAAC;wGACC,WAAU;wGACV,OAAM;wGACN,MAAK;wGACL,SAAQ;;0HAER,6LAAC;gHACC,WAAU;gHACV,IAAG;gHACH,IAAG;gHACH,GAAE;gHACF,QAAO;gHACP,aAAY;;;;;;0HAEd,6LAAC;gHACC,WAAU;gHACV,MAAK;gHACL,GAAE;;;;;;;;;;;;;;;;6GAIN,iBAAiB,MAAM,GAAG,IAC5B,iBAAiB,GAAG,CAAC,CAAC,uBACpB,6LAAC,qIAAA,CAAA,aAAU;wGAAiB,OAAO,OAAO,KAAK;kHAC5C,OAAO,KAAK;uGADE,OAAO,EAAE;;;;gIAK5B,6LAAC;oGAAI,WAAU;8GAAgC;;;;;;;;;;;;;;;;;kGAIrD,6LAAC,mIAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;wDASlC,kBAAkB,mCACjB,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC,mIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,kBACN,6LAAC,mIAAA,CAAA,OAAI;4EAAC,WAAU;;8FACd,6LAAC,mIAAA,CAAA,aAAU;oFAAC,WAAU;;sGACpB,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAoC;;;;;;sGACzD,6LAAC,mIAAA,CAAA,kBAAe;4FAAC,WAAU;sGAAgB;;;;;;;;;;;;8FAIjD,6LAAC,mIAAA,CAAA,cAAW;8FACV,cAAA,6LAAC,mIAAA,CAAA,WAAQ;;0GACP,6LAAC,mIAAA,CAAA,cAAW;0GACV,cAAA,6LAAC;;wGACE,6BACC,6LAAC;4GAAI,WAAU;sHACb,cAAA,6LAAC;gHAAE,WAAU;0HAAwB;;;;;;;;;;;wGAGxC,CAAC,gBAAgB,CAAC,uBACjB,6LAAC,qIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAS;4GACT,WAAU;;8HAEV,6LAAC,yMAAA,CAAA,SAAM;oHAAC,WAAU;;;;;;gHAAiB;;;;;;;wGAItC,8BACC,6LAAC;4GAAI,WAAU;;8HACb,6LAAC;oHACC,KAAK;oHACL,QAAQ;oHACR,WAAW;oHACX,WAAU;;;;;;8HAEZ,6LAAC;oHAAI,WAAU;;sIACb,6LAAC,qIAAA,CAAA,SAAM;4HACL,MAAK;4HACL,SAAS;4HACT,SAAQ;4HACR,WAAU;;8IAEV,6LAAC,uMAAA,CAAA,QAAK;oIAAC,WAAU;;;;;;gIAAiB;;;;;;;sIAGpC,6LAAC,qIAAA,CAAA,SAAM;4HACL,MAAK;4HACL,SAAS;4HACT,SAAQ;4HACR,WAAU;;8IAEV,6LAAC,+LAAA,CAAA,IAAC;oIAAC,WAAU;;;;;;gIAAiB;;;;;;;;;;;;;;;;;;;wGAMrC,CAAC,gBAAgB,CAAC,aAAa,SAAS,SAAS,KAAK,mBACrD,6LAAC;4GAAI,WAAU;;8HACb,6LAAC;oHAAI,WAAU;8HACb,cAAA,6LAAC;wHAAI,WAAU;kIACZ,CAAC;4HACA,MAAM,eAAe,SAAS,aAAa,SAAS;4HACpD,IAAI,cAAc;gIAChB,qBACE,6LAAC,gIAAA,CAAA,UAAK;oIACJ,KACE,aAAa,UAAU,CAAC,WACpB,eACA,aAAa,UAAU,CAAC,UACxB,eACA,GAAG,8DAAwC,2BAA2B,aAAa,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;oIAEpH,KAAI;oIACJ,QAAQ;oIACR,OAAO;oIACP,WAAU;oIACV,OAAO;wIAAE,QAAQ;wIAAQ,OAAO;oIAAO;oIACvC,aAAa,aAAa,UAAU,CAAC;;;;;;4HAG3C;4HACA,qBACE,6LAAC;gIAAI,WAAU;0IACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oIAAC,WAAU;;;;;;;;;;;wHAGxB,CAAC;;;;;;;;;;;8HAGL,6LAAC,qIAAA,CAAA,SAAM;oHACL,MAAK;oHACL,SAAS;wHACP,SAAS;wHACT,eAAe;wHACf,SAAS,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE;wHAC5B,KAAK,QAAQ,CAAC,SAAS;wHACvB;oHACF;oHACA,SAAQ;oHACR,WAAU;;sIAEV,6LAAC,yMAAA,CAAA,SAAM;4HAAC,WAAU;;;;;;wHAAiB;;;;;;;;;;;;;sHAKzC,6LAAC;4GAAO,KAAK;4GAAW,OAAO;gHAAE,SAAS;4GAAO;;;;;;;;;;;;;;;;;0GAGrD,6LAAC,mIAAA,CAAA,kBAAe;gGAAC,WAAU;0GAA6B;;;;;;0GAGxD,6LAAC,mIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EAMjC,6LAAC,mIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,OAAI;4EAAC,WAAU;;8FACd,6LAAC,mIAAA,CAAA,aAAU;oFAAC,WAAU;;sGACpB,6LAAC,mIAAA,CAAA,YAAS;4FAAC,WAAU;sGAAoC;;;;;;sGACzD,6LAAC,mIAAA,CAAA,kBAAe;4FAAC,WAAU;sGAAgB;;;;;;;;;;;;8FAI7C,6LAAC,mIAAA,CAAA,cAAW;8FACV,cAAA,6LAAC,mIAAA,CAAA,WAAQ;;4FACN,CAAC,iCACA,6LAAC,mIAAA,CAAA,cAAW;0GACV,cAAA,6LAAC;oGAAI,WAAU;8GACb,cAAA,6LAAC;wGAAM,WAAU;;0HACf,6LAAC;gHAAI,WAAU;;kIACb,6LAAC,yMAAA,CAAA,SAAM;wHAAC,WAAU;;;;;;kIAClB,6LAAC;wHAAE,WAAU;;0IACX,6LAAC;gIAAK,WAAU;0IAAgB;;;;;;4HAAsB;;;;;;;kIAExD,6LAAC;wHAAE,WAAU;kIAAwB;;;;;;;;;;;;0HAEvC,6LAAC,oIAAA,CAAA,QAAK;gHACJ,IAAG;gHACH,MAAK;gHACL,QAAO;gHACP,WAAU;gHACV,UAAU,CAAC;oHACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oHAChC,IAAI,MAAM;wHACR,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;4HAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4HACZ;wHACF;wHACA,MAAM,kBAAkB;4HACtB,MAAM,KAAK,IAAI;4HACf,MAAM,KAAK,IAAI;4HACf,MAAM,KAAK,IAAI;4HACf,KAAK,IAAI,eAAe,CAAC;wHAC3B;wHACA,oBAAoB;wHACpB,qBAAqB;wHACrB,MAAM,QAAQ,CAAC;oHACjB;gHACF;;;;;;;;;;;;;;;;;;;;;uHAMR,6LAAC;gGAAI,WAAU;0GACb,cAAA,6LAAC;oGAAI,WAAU;;sHACb,6LAAC;4GAAI,WAAU;;8HACb,6LAAC;oHAAI,WAAU;8HACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wHAAC,WAAU;;;;;;;;;;;8HAEtB,6LAAC;;sIACC,6LAAC;4HAAE,WAAU;sIAAqC,iBAAiB,IAAI;;;;;;sIACvE,6LAAC;4HAAE,WAAU;sIACV,4BAA4B,OACzB,eAAe,iBAAiB,IAAI,IACpC;;;;;;;;;;;;;;;;;;sHAIV,6LAAC;4GAAI,WAAU;;gHACZ,oBAAoB,SAAS,kCAC5B,6LAAC,qIAAA,CAAA,SAAM;oHACL,MAAK;oHACL,SAAQ;oHACR,MAAK;oHACL,SAAS,IAAM,OAAO,IAAI,CAAC,iBAAiB,GAAG,EAAE;oHACjD,WAAU;8HACX;;;;;;8HAIH,6LAAC,qIAAA,CAAA,SAAM;oHACL,MAAK;oHACL,SAAQ;oHACR,MAAK;oHACL,SAAS;oHACT,WAAU;8HAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wHAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0GAMvB,6LAAC,mIAAA,CAAA,kBAAe;gGAAC,WAAU;0GAA6B;;;;;;0GAGxD,6LAAC,mIAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAUjC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,UAAU;gEACV,WAAU;0EAET,6BACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;4EAAuB,SAAQ;;8FAC5C,6LAAC;oFAAO,WAAU;oFAAa,IAAG;oFAAK,IAAG;oFAAK,GAAE;oFAAK,QAAO;oFAAe,aAAY;;;;;;8FACxF,6LAAC;oFAAK,WAAU;oFAAa,MAAK;oFAAe,GAAE;;;;;;;;;;;;wEAC/C;;;;;;2EAGN,cACF,mBAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAapB,6LAAC,sIAAA,CAAA,UAAM;;;;;;;AAGb;GA51CM;;QACW,qIAAA,CAAA,YAAS;QACP,4JAAA,CAAA,cAAW;QACP,qIAAA,CAAA,kBAAe;QAea,4JAAA,CAAA,cAAW;QAU/C,iKAAA,CAAA,UAAO;;;KA5BhB;AA81CN,MAAM,qBAAqB;IACzB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QACP,wBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;;;;;;kBAMV,cAAA,6LAAC;;;;;;;;;;AAGP;MAxBM;uCA0BS", "debugId": null}}]}